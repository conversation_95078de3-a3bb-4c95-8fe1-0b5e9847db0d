import logging
from concurrent_log_handler import ConcurrentRotatingFileHandler
import os

from conf.config import settings


def get_logger(name: str) -> logging.Logger:
    # 确保日志目录存在
    log_dir = settings.LOG_DIR
    log_path = os.path.join(log_dir, name)
    if not os.path.exists(log_path):
        os.makedirs(log_path)

    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # 创建处理器，按文件大小轮转，最大 50MB，保留 30 个备份文件
    handler = ConcurrentRotatingFileHandler(
        filename=os.path.join(log_dir, name, f"{name}.log"),
        maxBytes=50 * 1024 * 1024,
        backupCount=30,
        encoding="utf-8"
    )
    handler.setLevel(logging.INFO)

    # 创建一个handler，用于输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)  # 设置控制台handler的日志级别

    # 创建日志格式
    formatter = logging.Formatter(
        '%(pathname)s %(funcName)s (%(lineno)d) %(thread)d - %(asctime)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)  # 为控制台handler也设置格式

    # 将处理器添加到日志记录器
    logger.addHandler(handler)
    logger.addHandler(console_handler)
    return logger


# 创建不同用途的日志实例
error_logger = get_logger("error")
task_logger = get_logger("task")
resource_logger = get_logger("resource")
prompt_logger = get_logger("prompt")
ws_producer_logger = get_logger("ws_producer")
task_monitor_logger = get_logger("monitor")