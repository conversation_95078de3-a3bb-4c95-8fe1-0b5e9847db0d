import json
from datetime import datetime

from bson import ObjectId
from starlette import status
from starlette.responses import JSONResponse
from typing import Union


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, ObjectId):
            return str(obj)
        return super().default(obj)


def resp_200(*, data: Union[list, dict, str] = None) -> JSONResponse:
    content = {
        'code': 200,
        'result': data,
        'message': "Success",
    }
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=json.loads(json.dumps(content, cls=CustomJSONEncoder)),
    )


def resp_400(*, data: str = None, message: str = "BAD REQUEST") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            'code': 400,
            'result': data,
            'message': message,
        }
    )


def resp_402(*, data: str = None, message: str = "PAYMENT_REQUIRED") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_402_PAYMENT_REQUIRED,
        content={
            'code': 402,
            'result': data,
            'message': message,
        }
    )


def resp_403(*, data: str = None, message: str = "FORBIDDEN") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_403_FORBIDDEN,
        content={
            'code': 403,
            'result': data,
            'message': message,
        }
    )


def resp_404(*, data: str = None, message: str = "NOT_FOUND") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={
            'code': 404,
            'result': data,
            'message': message,
        }
    )


def resp_405(*, data: str = None, message: str = "METHOD_NOT_ALLOWED") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
        content={
            'code': 405,
            'result': data,
            'message': message,
        }
    )


def resp_450(*, data: str = None, message: str = "") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 450,
            'result': data,
            'message': message,
        }
    )


def resp_500(*, data: str = None, message: str = "INTERNAL_SERVER_ERROR") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            'code': 500,
            'result': data,
            'message': message,
        }
    )

def resp_4210(*, data: Union[list, dict, str] = None, message: str = "") -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 4210,
            'result': data,
            'message': message,
        }
    )
