from typing import Union
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from fastapi import HTTPException
from slowapi.errors import RateLimitExceeded
from starlette.requests import Request
from starlette.responses import J<PERSON><PERSON>esponse


async def http_error_handler(_: Request, exc: HTTPException) -> JSONResponse:
    return JSONResponse(
        status_code=exc.status_code,
        content={
            'code': exc.status_code,
            'result': None,
            'message': exc.detail
        }
    )


async def http422_error_handler(
        _: Request, exc: Union[RequestValidationError, ValidationError]
) -> JSONResponse:
    return JSONResponse(
        status_code=200,
        content={
            'code': 400,
            'result': None,
            'message': exc.errors()[0].get('msg') if len(exc.errors()) > 0 else exc.errors()
        }
    )


async def rate_limit_exceeded_handler(request: Request, exc: RateLimitExceeded):
    return JSONResponse(
        status_code=429,
        content={
            'code': 429,
            'result': None,
            'message': f"{exc.detail}"
        }
    )
