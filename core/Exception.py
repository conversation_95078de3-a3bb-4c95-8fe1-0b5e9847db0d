from typing import Union
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from fastapi import HTTPException
from starlette.requests import Request
from starlette.responses import J<PERSON><PERSON>esponse


async def http_error_handler(_: Request, exc: HTTPException) -> JSONResponse:
    return JSONResponse(
        status_code=exc.status_code,
        content={
            'code': exc.status_code,
            'result': None,
            'message': exc.detail
        }
    )


async def http422_error_handler(
        _: Request, exc: Union[RequestValidationError, ValidationError]
) -> JSONResponse:
    errors = exc.errors()
    if len(errors) > 0:
        try:
            error_msg = f"{errors[0]['loc'][1]}: {errors[0]['msg']}"
        except Exception:
            error_msg = errors[0]['msg']
    else:
        error_msg = ""
    return JSONResponse(
        status_code=200,
        content={
            'code': 400,
            'result': None,
            'message': error_msg
        }
    )
