from fastapi import APIRouter

from apps.id_photo.views import router as id_photo_router
from apps.user.views import router as user_router
from apps.gen.views import router as gen_router
from apps.common.views import router as common_router

router = APIRouter(prefix='/api')

router.include_router(id_photo_router, prefix='/id_photo', tags=['图片相关'])
router.include_router(gen_router, prefix='/gen', tags=['生图'])
router.include_router(gen_router, prefix='/id_photo', tags=['生图'])
router.include_router(user_router, prefix='/user', tags=['用户'])
router.include_router(common_router, prefix='/common', tags=['公共'])
