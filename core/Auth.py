import json

from fastapi import Request, HTTPException
from starlette.status import HTTP_403_FORBIDDEN

from databases.redis import async_user_redis
from services.jwt import get_username_from_token
from services.user import get_user_by_username

from utils.fmts import *


async def get_authorization(req: Request):
    """
    Get username and token from request
    必须有token
    """
    apikey = req.headers.get('authorization', "") or req.headers.get("Authorization", "")

    if not apikey:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail="authorization token not found"
        )
    return await get_authorization_base(apikey)


async def get_authorization_allow_not_auth(req: Request):
    """
    Get username and token from request
    可以不传token  就返回{}和""
    """
    apikey = req.headers.get('authorization', "") or req.headers.get("Authorization", "")

    if not apikey:
        return {}, ""

    return await get_authorization_base(apikey)


async def get_authorization_base(apikey):
    try:
        token_prefix, token = apikey.split()
    except ValueError:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail="authorization error-1"
        )
    if token_prefix != 'JWT':
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail='authorization error-2'
        )
    logout_key = LOGOUT_TOKEN_FMT.format(token)
    if await async_user_redis.exists(logout_key):
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail='authorization error-3'
        )
    username = get_username_from_token(token)
    if not username:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail='authorization error-4'
        )
    userinfo_key = USER_INFO_FMT.format(username)
    userinfo = await async_user_redis.get(userinfo_key)
    userinfo = json.loads(userinfo) if userinfo else {}
    if not userinfo:
        user = await get_user_by_username(username)
        if user is None:
            raise HTTPException(
                status_code=HTTP_403_FORBIDDEN, detail='authorization error-5'
            )
        userinfo = user.to_dict('username', 'email', 'id')
        await async_user_redis.set(userinfo_key, json.dumps(userinfo), ex=60 * 60 * 24)
    return userinfo, token
