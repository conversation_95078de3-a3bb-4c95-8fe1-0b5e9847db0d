from fastapi import Request, HTTPException
from starlette.status import HTTP_403_FORBIDDEN
from conf.settings import secret_auth_map


async def get_authorization(req: Request):
    """
    Get username and token from request
    必须有token
    """
    apikey = req.headers.get('authorization', "") or req.headers.get("Authorization", "")
    api_username = secret_auth_map.get(apikey)
    if not api_username:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail="authorization token not found"
        )
    return {"username": api_username, "apikey": apikey}
