import pika


def singleton(cls):
    instances = {}

    def wrapper(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return wrapper


class TaskProducerConsumer:
    def __init__(
            self,
            username,
            password,
            host='127.0.0.1',
            port=5672,
            vhost='comfy',
            exchange='aiease',
            routing_key='',
    ):
        self.connection_params = pika.ConnectionParameters(
            host=host,  # RabbitMQ 服务器地址，默认本地运行
            port=port,  # 默认端口
            virtual_host=vhost,  # 你想连接的虚拟主机
            heartbeat=30,
            credentials=pika.PlainCredentials(username, password)  # 用户名和密码
        )
        self.exchange = exchange
        self.queue_name = ""
        self.routing_key = routing_key
        self.channel = self._connect()

    def _connect(self):
        connection = pika.BlockingConnection(self.connection_params)
        channel = connection.channel()
        # 指定交换机名称和类型
        channel.exchange_declare(exchange=self.exchange, exchange_type='direct', durable=True)
        result = channel.queue_declare(queue='', exclusive=True)
        self.queue_name = result.method.queue
        return channel

    def send_task(self, routing_key, task_data):
        """ 发送任务到 RabbitMQ """
        self.channel.basic_publish(
            exchange=self.exchange,
            routing_key=routing_key,
            body=task_data
        )
        print(f"Sent message to {routing_key}")

    def start_consumer(self):
        """ 启动消费者，处理来自其他生产者的任务 """
        self.channel.basic_qos(prefetch_count=1000)
        self.channel.queue_bind(exchange=self.exchange, queue=self.queue_name, routing_key=self.routing_key)
        self.channel.basic_consume(queue=self.queue_name, on_message_callback=self.callback, auto_ack=True)
        print("Waiting for tasks...")
        self.channel.start_consuming()

    def callback(self, ch, method, properties, body):
        """ 处理收到的任务，并向发送方回调结果 """
        print(f"Received task: {body.decode()}")
