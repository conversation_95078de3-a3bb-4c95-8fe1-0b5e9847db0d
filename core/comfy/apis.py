import json
import os
from functools import lru_cache

from conf.settings import DEFAULT_CLIENT_ID, NODES_DOWNLOAD_IMAGE
from core.Log import error_logger
from utils.lock_utils import lock_call_api

from utils.utils import get_seed


class BaseComfyUi(object):

    def __init__(self, server_address, **kwargs):
        self.prompt_id = ""
        self.prompt = ""
        self.server_address = server_address
        self.client_id = DEFAULT_CLIENT_ID
        self.timeout = 30
        self.number = kwargs.get('number', None)
        self.lock_key = f"lock:{self.server_address}"

    def queue_prompt(self, prompt):
        """
        任务推向队列
        :param prompt:
        :return:
        """
        data = {"prompt": prompt, "client_id": self.client_id}
        if self.number is not None:
            data["number"] = self.number
        url = f"http://{self.server_address}/prompt"
        response = lock_call_api(lock_key=self.lock_key, url=url, data=data)
        return response.json()

    def upload_image_by_url(self, img_url):
        if NODES_DOWNLOAD_IMAGE:
            return {'name': img_url}
        data = {'img_url': img_url}
        url = f'http://{self.server_address}/upload/by_url_image'
        response = lock_call_api(lock_key=self.lock_key, url=url, data=data)
        response.raise_for_status()
        return response.json()

    @staticmethod
    def get_base_prompt(filename: str) -> dict:
        dir_name = os.path.dirname(__file__)
        file_path = os.path.join(dir_name, 'workflows', filename)
        with open(file_path) as f:
            prompt = json.load(f)
        if NODES_DOWNLOAD_IMAGE:
            # 直接在节点上下载图片，不去调用comfyui上传图片接口
            prompt_str = json.dumps(prompt)
            prompt_str = prompt_str.replace(r'"LoadImage"', r'"AIEaseLoadImage"').replace(r'"LoadImageMask"',
                                                                                          r'"AIEaseLoadImageMask"')
            prompt = json.loads(prompt_str)
        return prompt


class Img2ImgToolComfy(BaseComfyUi):
    """
    图生图工具类
    """
    # 类型对应的函数名
    type_funcname_dict = {
        'restore': 'get_prompt_restore',  # 修复+上色
        'rembg': 'get_prompt_rembg',  # 去背景
        'bg_blur': 'get_prompt_rembg',  # 背景虚化
        'upscale': 'get_prompt_upscale',  # 图片放大
        'enhance': 'get_prompt_enhance',  # 图片增强
        'passport': 'get_prompt_passport',  # 证件照
        'headshot': 'get_prompt_headshot',  # 头像
        'face_crop': 'get_prompt_face_crop',  # 人脸裁剪
        'face_swap': 'get_prompt_face_swap',  # 人脸换脸
        'object_remove': 'get_prompt_object_remove',  # 物体移除
        'text_remove': 'get_prompt_text_remove',  # 文字移除
        'ai_filter': 'get_prompt_ai_filter',  # ai滤镜
        'ai_baby': 'get_prompt_ai_baby',  # ai baby
        'object_swap': 'get_prompt_object_swap',  # 换物
        'hair_swap': 'get_prompt_hair_swap',  # 换发型
        'ai_bg': 'get_prompt_ai_bg',  # ai换背景
        'ai_enlarge': 'get_prompt_ai_enlarge',  # ai扩图
        'ai_skin_repair': 'get_prompt_ai_skin_repair',  # ai皮肤修复
        'ai_recolor': 'get_prompt_ai_recolor',  # ai皮肤修复
        'ai_face_cutout': 'get_prompt_ai_face_cutout',  # ai切脸
    }

    def gen(self, *, source_url, **kwargs):
        funcname = self.type_funcname_dict.get(kwargs.get('_type'))
        prompt_func = getattr(self, funcname, None)
        file_info = self.upload_image_by_url(source_url)
        file_name = file_info['name']
        prompt = prompt_func(file_name=file_name, **kwargs)
        self.prompt_id = self.queue_prompt(prompt)['prompt_id']
        return self.prompt_id

    def get_prompt_restore(self, *, file_name, **kwargs):
        """
        修复
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('restore.json')
        restore = kwargs.get('restore')  # 修复开关
        recolor = kwargs.get('recolor')  # 上色开关
        prompt['16']['inputs']['image'] = file_name
        if restore is not None:
            prompt['11']['inputs']['enabled'] = restore
        if recolor is not None:
            prompt['15']['inputs']['enabled'] = recolor
        return prompt

    def get_prompt_rembg(self, *, file_name, **kwargs):
        """
        去背景
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('rembg.json')
        prompt['3']['inputs']['image'] = file_name
        self.prompt = prompt
        return prompt

    def get_prompt_upscale(self, *, file_name, **kwargs):
        """
        图片放大
        :param file_name:
        :param kwargs:
        :return:
        """
        size = kwargs.get('size', 2)
        prompt = self.get_base_prompt('upscale_api.json')
        prompt['10']['inputs']['image'] = file_name
        prompt['16']['inputs']['scale_by'] = float(size) / 4
        return prompt

    def get_prompt_enhance(self, *, file_name, **kwargs):
        """
        图片增强
        :param file_name:
        :param kwargs:
        :return:
        """
        mode = kwargs.get('mode')  # 是否是去背景超分
        if mode == 'rembg':
            size = kwargs.get('size', '')
            file = 'rembg_enhance.json'
            prompt = self.get_base_prompt(file)
            prompt['64']['inputs']['image'] = file_name
            if str(size) == '2':
                prompt['28']['inputs']['upscale_model'] = 'x2'
                prompt['28']['inputs']['upscale'] = 2
                prompt['76']['inputs']['scale_by'] = 2
            elif str(size) == '4':
                prompt['28']['inputs']['upscale_model'] = 'x4'
                prompt['28']['inputs']['upscale'] = 4
                prompt['76']['inputs']['scale_by'] = 4
            return prompt

        face_restore = kwargs.get('face_restore')  # 修复开关
        if face_restore:
            # 脸部修复使用restore的json
            file = 'restore.json'
            prompt = self.get_base_prompt(file)
            recolor = kwargs.get('recolor')  # 上色开关
            prompt['16']['inputs']['image'] = file_name
            prompt['11']['inputs']['enabled'] = True
            prompt['11']['inputs']['upscale'] = 4
            prompt['11']['inputs']['upscale_model'] = "x4"
            prompt['11']['inputs']['codeformer_fidelity'] = 0.8
            if recolor is not None:
                prompt['15']['inputs']['enabled'] = recolor
            return prompt
        else:
            file = 'enhance.json'
            prompt = self.get_base_prompt(file)
            model = kwargs.get('model')  # upscale模型名
            recolor = kwargs.get('recolor')  # 上色开关
            prompt['15']['inputs']['image'] = file_name
            if model is not None:
                prompt['21']['inputs']['model_name'] = model

            if face_restore is not None:
                prompt['23']['inputs']['enabled'] = face_restore

            if recolor is not None:
                prompt['37']['inputs']['enabled'] = recolor
            return prompt

    def get_prompt_passport(self, *, file_name, **kwargs):
        """
        新版passport
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('passport_photo_maker.json')
        prompt['3']['inputs']['image'] = file_name
        return prompt

    def get_prompt_headshot(self, *, file_name, **kwargs):
        """
        新版headshot
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('headshot.json')

        seed = kwargs.get('seed', get_seed())
        batch_size = kwargs.get('batch_size', 1)
        width = kwargs.get('width')
        height = kwargs.get('height')
        cfg = kwargs.get('cfg')
        step = kwargs.get('steps')
        sampler_name = kwargs.get('sampler_name')
        scheduler = kwargs.get('scheduler')
        pulid_method = kwargs.get('pulid_method')
        pulid_weight = kwargs.get('pulid_weight')
        text = kwargs.get('prompt')
        negative_text = kwargs.get('negative_prompt')

        prompt['3']['inputs'].update({
            'seed': seed,
            'cfg': cfg,
            'steps': step,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
        })
        prompt['5']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size,
        })
        prompt['33']['inputs'].update({
            'method': pulid_method,
            'weight': pulid_weight,
        })

        prompt['12']['inputs']['image'] = file_name

        prompt['22']['inputs']['text'] = text
        prompt['23']['inputs']['text'] = negative_text
        return prompt

    def get_prompt_face_crop(self, *, file_name, **kwargs):
        """
        人脸裁剪
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('face_crop.json')
        prompt['15']['inputs']['image'] = file_name
        return prompt

    def get_prompt_face_swap(self, *, file_name, **kwargs):
        """
        换脸
        :param file_name:
        :param kwargs:
        :return:
        """
        input_index = kwargs.get('input_index')
        source_index = kwargs.get('source_index')
        source_urls = kwargs.get('source_urls')
        prompt = self.get_base_prompt('face_swap.json')
        prompt['15']['inputs']['image'] = file_name
        prompt['13']['inputs']['input_faces_index'] = input_index
        prompt['13']['inputs']['source_faces_index'] = source_index
        prompt['14']['inputs']['url_dir'] = source_urls
        return prompt

    def get_prompt_text_remove(self, *, file_name, **kwargs):
        """
        去水印
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('text_remove.json')
        seed = kwargs.get('seed', get_seed())
        prompt['71']['inputs']['image'] = file_name
        prompt['33']['inputs']['seed'] = seed
        return prompt

    def get_prompt_object_remove(self, *, file_name, **kwargs):
        """
        局部去物
        :param file_name:
        :param kwargs:
        :return:
        """
        mask_url = kwargs.get('mask_url', '')
        custom = kwargs.get('custom', '')
        seed = kwargs.get('seed', get_seed())
        if mask_url:
            try:
                mask_file_info = self.upload_image_by_url(mask_url)
            except Exception as e:
                error_logger.error(f'object remove upload mask image failed: {e}')
                raise e
            mask_file_name = mask_file_info['name']
            prompt = self.get_base_prompt('object_remove_mask.json')
            prompt['11']['inputs']['image'] = file_name
            prompt['103']['inputs']['image'] = mask_file_name
            prompt['3']['inputs']['seed'] = seed
        else:
            prompt = self.get_base_prompt('object_remove_detect.json')
            prompt['105']['inputs']['image'] = file_name
            prompt['131']['inputs']['seed'] = seed
            prompt['108']['inputs']['prompt'] = custom
        return prompt

    def get_prompt_ai_filter(self, *, file_name, **kwargs):
        """
        ai滤镜
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('ai_filter.json')

        seed = kwargs.get('seed', get_seed())
        base_model = kwargs.get('base_model')
        lora = kwargs.get('lora')

        cfg = kwargs.get('cfg')
        denoise = kwargs.get('denoise')

        strength_model = kwargs.get('strength_model')
        strength_clip = kwargs.get('strength_clip')

        text_a = kwargs.get('text_a')
        text_c = kwargs.get('text_c')

        cn_strength = kwargs.get('cn_strength')
        cn_start = kwargs.get('cn_start')
        cn_end = kwargs.get('cn_end')

        negative_text = kwargs.get('negative_prompt')

        task_type = kwargs.get('task_type', 'caption')
        task_token = kwargs.get('task_token', 1024)

        prompt['7']['inputs']['text'] = negative_text  # 负向提示词

        prompt['3']['inputs'].update({
            'seed': seed,
            'cfg': cfg,
            'denoise': denoise
        })

        prompt['71']['inputs']['task'] = task_type
        prompt['71']['inputs']['max_new_tokens'] = task_token

        prompt['21']['inputs']['image'] = file_name

        prompt['69']['inputs']['text_a'] = text_a
        prompt['69']['inputs']['text_c'] = text_c

        prompt['27']['inputs']['ckpt_name'] = base_model  # 基础模型名字

        # lora配置
        prompt['26']['inputs'].update({
            'lora_name': lora,
            'strength_model': strength_model,
            'strength_clip': strength_clip
        })
        # ControINet配置
        prompt['28']['inputs'].update({
            'strength': cn_strength,
            'start_percent': cn_start,
            'end_percent': cn_end
        })
        return prompt

    def get_prompt_ai_baby(self, *, file_name, **kwargs):
        """
        ai baby
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('ai_baby.json')

        seed = kwargs.get('seed', get_seed())

        prompt_text = kwargs.get('prompt')

        weight = kwargs.get('weight')

        parent2 = kwargs.get('parent2')

        try:
            parent2_url = self.upload_image_by_url(parent2)['name']
        except Exception as e:
            error_logger.error(f'ai baby upload parent2 image failed: {e}')
            raise e

        prompt['19']['inputs']['text'] = prompt_text  # 正向提示词
        prompt['26']['inputs']['weight'] = weight  # 权重

        prompt['17']['inputs']['seed'] = seed

        prompt['10']['inputs']['image'] = file_name  # parent1
        prompt['11']['inputs']['image'] = parent2_url  # parent2

        return prompt

    def get_prompt_object_swap(self, *, file_name, **kwargs):
        """
        object swap
        :param file_name:
        :param kwargs:
        :return:
        """
        seed = kwargs.get('seed', get_seed())
        mask_url = kwargs.get('mask_url')
        source = kwargs.get('source')
        target = kwargs.get('target')
        batch_size = kwargs.get('batch_size', 2)
        if mask_url:
            try:
                mask_url_name = self.upload_image_by_url(mask_url)['name']
            except Exception as e:
                error_logger.error(f'ai baby upload mask_url image failed: {e}')
                raise e
            prompt = self.get_base_prompt('object_swap_mask.json')
            prompt['3']['inputs']['seed'] = seed
            prompt['6']['inputs']['text'] = target
            prompt['71']['inputs']['image'] = mask_url_name
            prompt['11']['inputs']['image'] = file_name
            prompt['91']['inputs']['amount'] = batch_size

        else:
            prompt = self.get_base_prompt('object_swap_detect.json')
            prompt['100']['inputs']['prompt'] = source
            prompt['6']['inputs']['text'] = target
            prompt['3']['inputs']['seed'] = seed
            prompt['96']['inputs']['image'] = file_name
            prompt['91']['inputs']['amount'] = batch_size

        return prompt

    def get_prompt_hair_swap(self, *, file_name, **kwargs):
        """
        object swap
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('hair_swap.json')

        seed = kwargs.get('seed', get_seed())
        style_prompt = kwargs.get('style_prompt')
        color_prompt = kwargs.get('color_prompt')
        prompt['12']['inputs']['image'] = file_name
        prompt['44']['inputs']['seed'] = seed
        prompt['1']['inputs']['seed'] = seed
        prompt['107']['inputs']['string'] = style_prompt
        prompt['108']['inputs']['string'] = color_prompt

        return prompt

    def get_prompt_ai_bg(self, *, file_name, **kwargs):
        """
        ai bg
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('ai_bg.json')
        seed = kwargs.get('seed', get_seed())
        positive_prompt = kwargs.get('prompt')
        negative_prompt = kwargs.get('negative_prompt')
        prompt['131']['inputs']['image'] = file_name
        prompt['127']['inputs']['seed'] = seed
        prompt['128']['inputs']['text'] = positive_prompt
        if negative_prompt:
            prompt['129']['inputs']['text'] = negative_prompt
        return prompt

    def get_prompt_ai_enlarge(self, *, file_name, **kwargs):
        """
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('ai_enlarge.json')
        seed = kwargs.get('seed', get_seed())
        position = kwargs.get('position', {})
        prompt['10']['inputs'].update(position)
        prompt['3']['inputs']['seed'] = seed
        prompt['11']['inputs']['image'] = file_name
        return prompt

    def get_prompt_ai_skin_repair(self, *, file_name, **kwargs):
        """
        ai skin repair
        :param file_name:
        :param kwargs:
        :return:
        """
        seed = kwargs.get('seed', get_seed())
        mask_url = kwargs.get('mask_url')
        if mask_url:
            try:
                mask_url_name = self.upload_image_by_url(mask_url)['name']
            except Exception as e:
                error_logger.error(f'ai baby upload mask_url image failed: {e}')
                raise e
            prompt = self.get_base_prompt('ai_skin_repair_mask.json')
            prompt['3']['inputs']['seed'] = seed
            prompt['1']['inputs']['image'] = file_name
            prompt['33']['inputs']['image'] = mask_url_name

        else:
            prompt = self.get_base_prompt('ai_skin_repair_detect.json')
            prompt['1']['inputs']['image'] = file_name

        return prompt

    def get_prompt_ai_recolor(self, *, file_name, **kwargs):
        """
        ai skin repair
        :param file_name:
        :param kwargs:
        :return:
        """
        seed = kwargs.get('seed', get_seed())
        position = kwargs.get('position', '')
        color = kwargs.get('color', '')
        prompt = self.get_base_prompt('ai_recolor.json')
        prompt['3']['inputs']['seed'] = seed
        prompt['12']['inputs']['image'] = file_name
        prompt['253']['inputs']['string'] = position
        prompt['257']['inputs']['Prepend'] = color
        return prompt

    def get_prompt_ai_face_cutout(self, *, file_name, **kwargs):
        """
        ai_face_cutout
        :param file_name:
        :param kwargs:
        :return:
        """

        prompt = self.get_base_prompt('ai_face_cutout.json')
        prompt['3']['inputs']['image'] = file_name

        return prompt


class Text2ImgToolComfy(BaseComfyUi):
    """
    文生图工具类
    """
    # 类型对应的函数名
    type_funcname_dict = {
        'art_v1': 'get_prompt_art_v1'
    }

    def gen(self, **kwargs):
        funcname = self.type_funcname_dict.get(kwargs.get('_type'))
        prompt_func = getattr(self, funcname, None)
        prompt = prompt_func(**kwargs)
        self.prompt_id = self.queue_prompt(prompt)['prompt_id']
        return self.prompt_id

    def get_prompt_art_v1(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('flux_art_v1.json')

        text = kwargs.get('prompt', '')
        width = kwargs.get('width', 1024)
        height = kwargs.get('height', 1024)
        batch_size = kwargs.get('batch_size', 2)
        seed = kwargs.get('seed', get_seed())
        prompt['59']['inputs']['seed'] = seed
        prompt['6']['inputs']['text'] = text
        prompt['72']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        return prompt


class Video2VideoToolComfy(BaseComfyUi):
    """
    视频生视频工具类
    """
    # 类型对应的函数名
    type_funcname_dict = {
        'video_text_remove': 'get_prompt_video_text_remove',  # 文字移除
    }

    def gen(self, **kwargs):
        funcname = self.type_funcname_dict.get(kwargs.get('_type'))
        prompt_func = getattr(self, funcname, None)
        if not prompt_func:
            return None
        prompt = prompt_func(**kwargs)
        self.prompt_id = self.queue_prompt(prompt)['prompt_id']
        return self.prompt_id

    def get_prompt_video_text_remove(self, **kwargs):
        prompt = self.get_base_prompt('video_remve_text_ai.json')

        return prompt


if __name__ == '__main__':
    ...
