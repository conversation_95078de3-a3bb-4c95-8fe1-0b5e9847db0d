{"10": {"inputs": {"image": "Elon_Musk_Royal_Society_(cropped).jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "11": {"inputs": {"image": "1200x675_cmsv2_51910cb5-bd27-5f30-8f60-8e7efed460c0-7962888.webp", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "17": {"inputs": {"seed": 194712698502192, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["26", 0], "positive": ["19", 0], "negative": ["20", 0], "latent_image": ["18", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "18": {"inputs": {"width": 896, "height": 1152, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "19": {"inputs": {"text": "", "clip": ["22", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "20": {"inputs": {"text": "(beard:1.1), earrings, bad teeth, twisted, watermark, hand, (naked), nsfw, low quality, wrinkle", "clip": ["22", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "21": {"inputs": {"samples": ["17", 0], "vae": ["22", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "22": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "24": {"inputs": {"pulid_file": "ip-adapter_pulid_sdxl_fp16.safetensors"}, "class_type": "PulidMode<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load PuLID Model"}}, "25": {"inputs": {}, "class_type": "PulidEvaClipLoader", "_meta": {"title": "<PERSON><PERSON> (PuLID)"}}, "26": {"inputs": {"method": "style", "weight": 0.5, "start_at": 0, "end_at": 1, "model": ["22", 0], "pulid": ["24", 0], "eva_clip": ["25", 0], "face_analysis": ["27", 0], "image": ["56", 0]}, "class_type": "A<PERSON>ly<PERSON><PERSON><PERSON>", "_meta": {"title": "Apply PuLID"}}, "27": {"inputs": {"provider": "CUDA"}, "class_type": "PulidInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID)"}}, "44": {"inputs": {"padding": 0, "padding_percent": 0.2, "index": 0, "analysis_models": ["47", 0], "image": ["63", 0]}, "class_type": "FaceBoundingBox", "_meta": {"title": "Face Bounding Box"}}, "47": {"inputs": {"library": "insightface", "provider": "CUDA"}, "class_type": "FaceAnalysisModels", "_meta": {"title": "Face Analysis Models"}}, "48": {"inputs": {"padding": 0, "padding_percent": 0.2, "index": 0, "analysis_models": ["47", 0], "image": ["64", 0]}, "class_type": "FaceBoundingBox", "_meta": {"title": "Face Bounding Box"}}, "56": {"inputs": {"image1": ["61", 0], "image2": ["62", 0]}, "class_type": "ImageBatchPadding", "_meta": {"title": "ImageBatch(letterbox)"}}, "61": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 0.5, "image": ["44", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "ImageScaleToTotalPixels"}}, "62": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 0.5, "image": ["48", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "ImageScaleToTotalPixels"}}, "63": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 0.5, "image": ["10", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "ImageScaleToTotalPixels"}}, "64": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 0.5, "image": ["11", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "ImageScaleToTotalPixels"}}, "67": {"inputs": {"filename_prefix": "aiease_aibaby", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["21", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}