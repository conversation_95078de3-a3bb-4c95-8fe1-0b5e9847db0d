from datetime import datetime
from typing import Callable

from fastapi import FastAP<PERSON>


def startup(app: FastAPI) -> Callable:
    """
    FastApi 启动完成事件
    :param app: FastAPI
    :return: start_app
    """

    async def app_start() -> None:
        print(f"App started at {datetime.now()}")

    return app_start


def stopping(app: FastAPI) -> Callable:
    """
    FastApi 停止事件
    :param app: FastAPI
    :return: stop_app
    """

    async def stop_app() -> None:
        print(f"App stopped at {datetime.now()}")

    return stop_app
