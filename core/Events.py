from typing import Callable
from fastapi import FastAPI
from databases.mysql import register_mysql


def startup(app: FastAPI) -> Callable:
    """
    FastApi 启动完成事件
    :param app: FastAPI
    :return: start_app
    """

    async def app_start() -> None:
        # APP启动完成后触发
        print('aiease api start ~~~')
        await register_mysql(app=app)

    return app_start


def stopping(app: FastAPI) -> Callable:
    """
    FastApi 停止事件
    :param app: FastAPI
    :return: stop_app
    """

    async def stop_app() -> None:
        print('aiease api stop ~~~')

    return stop_app
