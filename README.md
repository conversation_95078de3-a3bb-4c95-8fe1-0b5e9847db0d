### 1.项目介绍

```text
aiease
项目使用fastapi框架开发
线上和测试环境部署使用supervisor
本地调试直接运行run.py
python 版本是Python 3.10.14, 建议使用conda管理python环境
mysql作为数据库存储数据
缓存和中间件使用redis
```

### 2.环境安装

```text
1.安装conda
2.创建虚拟环境 conda create -n aiease python=3.10
3.激活虚拟环境 conda activate aiease
4.安装依赖 pip install -r requirements.txt
```

### 3.supervisor配置文件

```text
[program:aiease_server]
command=/home/<USER>/miniforge3/envs/aiease/bin/uvicorn main:app --workers 16 --log-level info --host 0.0.0.0 --port 9100  ; 启动服务的命令
directory=/home/<USER>/workspace/aiease   ; 服务所在的目录 
autostart=true                 ; 启动 Supervisor 时自动启动服务
autorestart=true               ; 服务意外退出时自动重启
stderr_logfile=/var/log/aiease_server_error.log  ; 错误日志文件 
stdout_logfile=/var/log/aiease_server_run.log  ; 标准输出日志文件 
user=easeus                  ; 运行服务的用户
environment=      ; 环境变量设置


[program:aiease_monitor]
command=/home/<USER>/miniforge3/envs/aiease/bin/python scripts/task_monitor.py  ; 启动服务的命令
directory=/home/<USER>/workspace/aiease   ; 服务所在的目录 
autostart=true                 ; 启动 Supervisor 时自动启动服务
autorestart=true               ; 服务意外退出时自动重启
stderr_logfile=/var/log/aiease_monitor_error.log  ; 错误日志文件 
stdout_logfile=/var/log/aiease_monitor_run.log  ; 标准输出日志文件 
user=easeus                  ; 运行服务的用户 
environment=      ; 环境变量设置

[program:aiease_celery]
command=/home/<USER>/miniforge3/envs/aiease/bin/celery -A celery_manage.tasks worker --loglevel=info -P threads
directory=/home/<USER>/workspace/aiease   ; 服务所在的目录
autostart=true                 ; 启动 Supervisor 时自动启动服务
autorestart=true               ; 服务意外退出时自动重启
stderr_logfile=/var/log/aiease_celery_error.log  ; 错误日志文件
stdout_logfile=/var/log/aiease_celery_run.log  ; 标准输出日志文件
user=easeus                  ; 运行服务的用户
environment=      ; 环境变量设置

[program:aiease_server2]
command=/home/<USER>/miniforge3/envs/aiease/bin/uvicorn main:app --workers 16 --log-level info --host 0.0.0.0 --port 9101  ; 启动服务的命令
directory=/home/<USER>/workspace/aiease   ; 服务所在的目录 
autostart=true                 ; 启动 Supervisor 时自动启动服务
autorestart=true               ; 服务意外退出时自动重启
stderr_logfile=/var/log/aiease_server2_error.log  ; 错误日志文件
stdout_logfile=/var/log/aiease_server2_run.log  ; 标准输出日志文件
user=easeus                  ; 运行服务的用户
environment=      ; 环境变量设置


```

各个环境的python环境和工作目录不一样, 请根据实际情况修改配置文件
以上配置是在生产环境的配置 工作目录在/home/<USER>/workspace/aiease python环境在/home/<USER>/mambaforge/envs/aiease

### 4.supervisor各个服务介绍

```text
1.aiease_server主要提供接口服务
2.aiease_monitor主要提供对接comfyui的websocket服务, 用于接收comfyui的信息,将websocket信息发送到celery中处理,建立一个长期不断的ws连接,会失败重试
2.aiease_celery主要是celery异步处理任务
```

### 5.supervisor命令介绍

```text
所有命令必须加上sudo
sudo supervisorctl status 查看所有服务状态
sudo supervisorctl stop xxxx 停止xxx服务
sudo supervisorctl start xxxx 启动xxx服务
sudo supervisorctl restart xxxx 重启xxx服务

重启aiease所有的服务使用以下命令
sudo supervisorctl restart aiease_celery aiease_celery_beat aiease_server aiease_monitor

(aiease) ubuntu@piclumen-s1:/etc/supervisor/conf.d$ sudo supervisorctl status
aiease_celery                   RUNNING   pid 3870808, uptime 16:58:23
aiease_monitor                  RUNNING   pid 3870865, uptime 16:58:19

RUNNING代表服务正在运行
uptime 表示运行时间
```

### 6.配置文件

```text
一些需要偶尔更改的配置文件一般都放在settings.yaml中
这个文件是被git给忽略的, 所以不会担心会覆盖
原理就是pyyaml读取后使用globals().update()将配置文件中的配置项加载到全局变量中
```

### 7.运行项目
```text
python run.py 运行项目
python task_monitor.py 运行task_monitor scripts目录下
```
### 重要发版记录

```text
1. 2024/09/13 face_swap_dev 换脸功能支持
2. 2024/09/23 去水印 物体擦除 访客用户功能支持
3. 2024/09/25 ai滤镜+数据库迁移
4. 2024/09/30 个人中心支持注销用户与删除图片历史
5. 2024/10/14 换脸,headshot,aifilter支持万圣节风格
6. 2024/10/19 新版本去背景+服务端上传+其他优化
7. 2024/10/25 图片增强功能支持
8. 2024/10/29 背景虚化支持+comfy支持对象存储
9. 2024/11/05 新版ai滤镜+增加3中滤镜风格
10. 2024/11/06 限制印度阿三等国家生图的优先级
11. 2024/11/08 ai baby功能支持
12. 2024/11/11 新restore功能支持
13. 2024/11/15 ai filter新风格增加+comfy自动下线+restore优化
14. 2024/11/19 ai换物上线
15. 2024/11/22 ai换发型上线 老照片修复回退 enhance优化
16. 2024/11/26 headshot风格调整,CUDA环境更新
17. 2024/11/28 ai换背景 torch环境更新 换发型优化
18. 2024/12/10 ai扩图
19. 2024/12/16 ai换肤
20. 2024/12/24 ai上色
```