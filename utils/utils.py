import asyncio
import logging
import random
import re
from urllib.parse import urlparse

import aiohttp

from conf.settings import aiease_allowed_domains, global_acceleration_cos_url
from databases.redis import async_cache_redis as cache
from services.server_source import get_comfy_resources
from utils.caches import get_all_comfy_queue_num, update_comfy_queue
from core.Log import error_logger

def get_seed() -> int:
    """
    获取随机seed
    Returns:

    """
    return random.randrange(100000000, 1000000000)


async def async_acquire_lock(lock_name, lock_timeout=10):
    # 尝试获取锁
    if await cache.setnx(lock_name, 'locked'):
        # 设置锁的过期时间，防止死锁
        await cache.expire(lock_name, lock_timeout)
        return True
    return False


async def get_resource_status(session: aiohttp.ClientSession, address: str) -> tuple:
    """
    访问comfy的接口 获取当前comfy的任务队列
    """
    try:
        url = f"http://{address}/queue"
        async with session.get(url) as response:
            result = await response.json()
            pending_length = len(result.get('queue_pending', []))
            running_length = len(result.get('queue_running', []))
            return address, pending_length + running_length
    except Exception as e:
        return address, None


async def get_resource(**kwargs):
    """
    获取服务器资源
    """
    tag = kwargs.get('tag')
    priority = kwargs.get('priority')
    if priority is None or priority >= 0:
        max_limit = 15
    else:
        max_limit = 15 - priority * 5
    comfy_resources = await get_comfy_resources()

    get_resource_lock = await async_acquire_lock('get_resource_lock', lock_timeout=300)  # 每300秒会重新去更新comfy当前队列状态
    if get_resource_lock:
        all_comfy_address = [i['address'] for i in comfy_resources if i.get('status')]
        queue_data = {}
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
            # 使用 asyncio.gather() 并发运行所有任务
            results = await asyncio.gather(*(get_resource_status(session, source) for source in all_comfy_address))
            for result in results:
                address, queue_len = result
                if queue_len is not None:
                    queue_data.update({address: queue_len})
                else:
                    from celery_manage.tasks import offline_resource
                    error_logger.log(logging.WARNING, f"查询队列状态失败导致，{address} offline")
                    offline_resource.delay(address)
        await update_comfy_queue(queue_data)  # 更新comfy任务队列

    if tag:
        # 如果传了tag 就找指定tag的服务器或者未[](表示所有类型任务都可使用)的服务器
        comfy_address = list({i['address'] for i in comfy_resources if i['tag'] == [] or tag in i['tag']})
    else:
        comfy_address = list({i['address'] for i in comfy_resources if i['tag'] == []})

    all_comfy_queue_dict = await get_all_comfy_queue_num()  # 所有comfy的任务队列数字典
    data = {k: v for k, v in all_comfy_queue_dict.items() if k in comfy_address}  # 当前所支持服务器的所有comfyui 任务数字典
    if not data:
        return None, 'Not enough server resources available. Please try again later after 1 minute'
    address = min(data, key=lambda x: data[x])  # 获取资源当前任务最少的一台comfy服务器
    min_addr_list = [k for k, v in data.items() if v == data[address]]  # 获取资源当前任务最少的机器
    min_address = random.choice(min_addr_list)  # 随机获取一台资源最少得服务器
    prompt_len = data[min_address]
    if prompt_len >= max_limit:
        return None, "Too many tasks. Please try again later."
    return min_address, ""


def is_url(s):
    # 定义URL正则表达式
    url_pattern = re.compile(
        r'^(https?|ftp):\/\/'  # 协议: http, https, ftp
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # 域名
        r'localhost|'  # 本地地址
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|'  # IPv4
        r'\[?[A-F0-9]*:[A-F0-9:]+\]?)'  # IPv6
        r'(?::\d+)?'  # 端口号
        r'(?:\/[^\s]*)?$',  # 资源路径
        re.IGNORECASE
    )

    # 检查字符串是否匹配URL正则表达式
    return re.match(url_pattern, s) is not None


def replace_global_acceleration_cos_domain(image_url: str):
    """
    替换成全球加速地址, 用于外网访问
    :param image_url:
    :return:
    """
    # 解析 URL，提取域名
    try:
        parsed_url = urlparse(image_url)
        domain = parsed_url.netloc
        # 校验域名是否在允许的域名列表中
        if domain in aiease_allowed_domains:
            image_url = image_url.replace(domain, global_acceleration_cos_url)
        return image_url
    except Exception as e:
        return image_url
