import time

import paramiko
from conf.settings import ssh_key_file_path, special_ssh_addr_port_map


class SSHClientWithKey:
    def __init__(self, host, port=22, username="root", ssh_key_file=ssh_key_file_path):
        """
        初始化SSH连接参数。

        :param host: 服务器地址
        :param port: SSH端口，默认22
        :param username: 用户名，默认root
        :param ssh_key_file: 私钥文件路径
        """
        self.hostname = host
        self.port = port
        self.username = username
        self.ssh_key_file = ssh_key_file
        self.ssh_client = None

    def __enter__(self):
        """
        进入上下文时，建立SSH连接。
        """
        self.ssh_client = paramiko.SSHClient()
        try:
            # 自动添加不在本地 `known_hosts` 文件中的主机
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            # 使用私钥文件加载密钥
            private_key = paramiko.RSAKey.from_private_key_file(self.ssh_key_file)
            # 连接服务器
            self.ssh_client.connect(self.hostname, port=self.port, username=self.username, pkey=private_key)
            return self
        except Exception as e:
            print(f"An error occurred during SSH connection setup: {e}")
            raise

    def execute_command(self, command):
        """
        执行命令并获取输出。

        :return: 命令执行的输出
        """
        try:
            # 执行命令
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            # 获取命令执行结果
            output = stdout.read().decode()
            error = stderr.read().decode()
            return output, error
        except Exception as e:
            print(f"An error occurred during command execution: {e}")
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        离开上下文时，自动关闭SSH连接。
        """
        if self.ssh_client:
            self.ssh_client.close()
            print("SSH connection closed.")


def restart_comfyui(address, port=22):
    """
    重启comfyui服务
    :param address:
    :param port:
    :return:
    """
    comfy_host = address.split(":")[0]
    comfy_port = address.split(":")[1]
    if comfy_host in special_ssh_addr_port_map:
        port = special_ssh_addr_port_map[comfy_host]
    with SSHClientWithKey(host=comfy_host, port=port) as ssh_client:
        # 重启之前的日志
        log_output1, _ = ssh_client.execute_command(f'tail -n 5 /var/log/comfyui/{comfy_port}_error.log')
        ssh_client.execute_command(f'supervisorctl restart comfyui{comfy_port}')
        time.sleep(25)
        # 重启之后的日志
        log_output2, _ = ssh_client.execute_command(f'tail -n 5 /var/log/comfyui/{comfy_port}_error.log')
        return log_output1, log_output2


# 使用示例
if __name__ == "__main__":
    hostname = "************"
    command = "ls -l"
