import requests

from databases.redis import cache_redis, async_cache_redis
import time
import functools


def acquire_lock(lock_name, lock_timeout=10):
    # 尝试获取锁
    if cache_redis.setnx(lock_name, 'locked'):
        # 设置锁的过期时间，防止死锁
        cache_redis.expire(lock_name, lock_timeout)
        return True
    return False


def release_lock(lock_name):
    """
    释放锁
    :param lock_name:
    :return:
    """
    cache_redis.delete(lock_name)


async def async_acquire_lock(lock_name, lock_timeout=10):
    # 尝试获取锁
    if await async_cache_redis.setnx(lock_name, 'locked'):
        # 设置锁的过期时间，防止死锁
        await async_cache_redis.expire(lock_name, lock_timeout)
        return True
    return False


class DistributedLock:
    def __init__(self, redis_client, lock_key, timeout=10, retry_interval=0.1):
        self.redis_client = redis_client
        self.lock_key = lock_key
        self.timeout = timeout
        self.retry_interval = retry_interval
        self.lock_value = f"{time.time()}-{id(self)}"

    def acquire(self):
        # Lua 脚本加锁操作
        script = """
        if redis.call("exists", KEYS[1]) == 0 then
            redis.call("setex", KEYS[1], ARGV[1], ARGV[2])
            return 1
        else
            return 0
        end
        """
        end_time = time.time() + self.timeout
        while time.time() < end_time:
            # 使用 Lua 脚本判断是否加锁成功
            result = self.redis_client.eval(script, 1, self.lock_key, self.timeout, self.lock_value)
            if result == 1:
                return True
            time.sleep(self.retry_interval)
        return False

    def release(self):
        # Lua 脚本解锁操作，确保只有持有锁的客户端才能释放锁
        script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            redis.call("del", KEYS[1])
            return 1
        else
            return 0
        end
        """
        result = self.redis_client.eval(script, 1, self.lock_key, self.lock_value)
        if result == 0:
            raise ValueError("Cannot release lock. Lock not held by this client.")


def distributed_lock(lock_key, timeout=10, retry_interval=0.1):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            lock = DistributedLock(cache_redis, lock_key, timeout, retry_interval)
            if lock.acquire():
                try:
                    return func(*args, **kwargs)
                finally:
                    lock.release()
            else:
                raise TimeoutError(f"Could not acquire lock for key: {lock_key}")

        return wrapper

    return decorator


def lock_call_api(url, lock_key, retry_interval=0.1, method="post", params=None, timeout=30, data=None, verify=False):
    """
    调用API时，使用分布式锁，防止重复调用
    :param method:
    :param url:
    :param lock_key:
    :param retry_interval:
    :param timeout:
    :param params:
    :param data:
    :param verify:
    :return:
    """
    params = params or {}
    data = data or {}
    lock = DistributedLock(cache_redis, lock_key, timeout, retry_interval)

    if lock.acquire():
        try:
            if method == "post":
                response = requests.post(url, json=data, timeout=timeout, verify=verify)
            else:
                response = requests.get(url, params=params, timeout=timeout, verify=verify)
            return response
        finally:
            lock.release()
    else:
        raise TimeoutError(f"Could not acquire lock for key: {lock_key}")
