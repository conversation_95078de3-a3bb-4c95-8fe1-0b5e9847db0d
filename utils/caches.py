import json

from databases.redis import async_task_redis, task_redis
from utils.fmts import *
from conf.settings import DEFAULT_CLIENT_ID

comfy_status_key = comfy_clientid_status_key_fmt.format(DEFAULT_CLIENT_ID)  # comfy运行服务状态key

comfy_queue_key = comfy_queue_status_key_fmt.format(DEFAULT_CLIENT_ID)  # comfy队列数状态key


async def get_all_comfy_status():
    """
    异步 获取所有comfy的状态
    :return:
    """
    all_status = {}
    servers_info = await async_task_redis.hgetall(comfy_status_key)
    for server, info_str in servers_info.items():
        all_status[server] = json.loads(info_str)
    return all_status


def sync_get_comfy_status(*, server):
    """
    同步 获取单一comfy的状态
    :param server:
    :return:
    """
    server_info = task_redis.hget(comfy_status_key, server)
    return json.loads(server_info) if server_info else {}


def sync_set_comfy_status(*, server, server_info):
    """
    同步 设置单一comfy的状态
    :param server:
    :param server_info:
    :return:
    """
    task_redis.hset(comfy_status_key, server, json.dumps(server_info))


async def get_all_comfy_queue_num():
    """
    异步 获取所有comfy的队列数
    :return:
    """
    comfy_num_dict = {}
    servers_info = await async_task_redis.hgetall(comfy_queue_key)
    for server, num in servers_info.items():
        try:
            comfy_num_dict[server] = int(num)
        except ValueError:
            ...
    return comfy_num_dict


async def set_comfy_queue_num(*, server, num):
    """
    同步 设置单一comfy的队列数
    :param server:
    :param num:
    :return:
    """
    await async_task_redis.hset(comfy_queue_key, server, num)


async def flush_comfy_queue_num():
    """
    清空comfy任务数队列
    :return:
    """

    await async_task_redis.delete(comfy_queue_key)


async def update_comfy_queue(queue_data):
    await flush_comfy_queue_num()  # 清空当前comfy任务数队列
    for server, num in queue_data.items():
        await set_comfy_queue_num(server=server, num=num)


def increment_task(*, server):
    """
    增加comfy队列
    :param server:
    :return:
    """
    if not server:
        return
    try:
        task_redis.hincrby(comfy_queue_key, server, 1)
    except Exception as e:
        print(e)


def decrement_task(*, server):
    """
    减少comfy队列
    :param server:
    :return:
    """
    if not server:
        return
    try:
        num = task_redis.hget(comfy_queue_key, server) or 0
        num = int(num)
        if num <= 0:
            task_redis.hset(comfy_queue_key, server, 0)
        else:
            task_redis.hincrby(comfy_queue_key, server, -1)
    except Exception as e:
        print(e)


def store_prompt_taskid(prompt_id, task_id):
    """
    保存prompt_id对应的task_id
    :param prompt_id:
    :param task_id:
    :return:
    """
    key = prompt_to_taskid_fmt.format(prompt_id)
    task_redis.set(key, task_id, ex=60 * 60)


def get_prompt_taskid(prompt_id):
    """
    获取prompt对应的task_id
    :param prompt_id:
    :return:
    """
    key = prompt_to_taskid_fmt.format(prompt_id)
    task_id = task_redis.get(key)
    return task_id


def store_prompt_comfy_server(prompt_id, comfy_server):
    """
    # 存储prompt对应的comfy地址
    :param prompt_id:
    :param comfy_server:
    :return:
    """

    key = prompt_comfy_address_fmt.format(prompt_id)
    task_redis.set(key, comfy_server, ex=60 * 60)


def get_prompt_comfy_server(prompt_id):
    """
    获取prompt对应的comfy地址
    :param prompt_id:
    :return:
    """
    key = prompt_comfy_address_fmt.format(prompt_id)
    address = task_redis.get(key)
    return address


def store_task_error(*, task_id, error):
    """
    存储prompt对应的错误信息 根据taskid存储
    :param task_id:
    :param error:
    :return:
    """
    if not task_id:
        return
    key = taskid_error_fmt.format(task_id)
    task_redis.set(key, error, ex=60 * 60)


def store_task_result(*, task_id, result):
    """
    存储prompt对应的结果 根据taskid存储
    :param task_id:
    :param result:
    :return:
    """

    if not task_id or not result:
        return
    if not isinstance(result, str):
        result = json.dumps(result)
    key = taskid_result_fmt.format(task_id)
    task_redis.set(key, result, ex=60 * 60)


def store_task_keypoint(*, task_id, keypoint):
    """
    存储prompt对应的面部找点结果 根据taskid存储
    :param task_id:
    :param keypoint:
    :return:
    """
    if not task_id or not keypoint:
        return
    if not isinstance(keypoint, str):
        keypoint = json.dumps(keypoint)
    key = taskid_keypoint_fmt.format(task_id)
    task_redis.set(key, keypoint, ex=60 * 60)
