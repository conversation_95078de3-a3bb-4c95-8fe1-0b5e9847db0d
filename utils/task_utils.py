import json
import re
import requests

from collections import defaultdict

from core.Log import task_logger, error_logger, prompt_logger
from services.task import sync_update_task,sync_get_task_info
from utils import strings
from utils.caches import store_task_result, store_task_keypoint, get_prompt_taskid
from utils.lock_utils import lock_call_api
from utils.time_utils import timer
from databases.redis import mq_redis
from conf.settings import tp_task_status

timeout_records = defaultdict(list)

MAX_TIMEOUTS = 20  # 阈值  15分钟连续失败创建失败20个任务就下架该服务器

TIME_WINDOW = 900  # 14分钟的窗口，单位为秒

prompt_taskid_cache = {}


def task_log(msg):
    """
    记录任务日志
    :param msg:
    :return:
    """
    try:
        prompt_logger.info(msg)
    except Exception:
        pass


def contains_no_face(a):
    # 使用正则表达式忽略大小写和空格
    pattern = re.compile(r'no\s*face', re.IGNORECASE)
    # 检测字符串中是否匹配
    return bool(pattern.search(a))


def get_error_msg(prompt_data):
    """
    获取轮训报错的错误信息
    :param prompt_data:
    :return:
    """
    error = prompt_data.get('exception_message', "")
    node_type = prompt_data.get('node_type')
    error_code = 5  # 表示已知的一些comfyui错误
    if node_type in ['Crop Face', "GET_KEY_POINTS", "skin_retouch", "FaceBoundingBox"]:
        # 切脸出错提示词
        comfy_error = strings.IMAGE_NO_FACE_ERROR
    elif contains_no_face(error) or 'object is not iterable' in error:
        comfy_error = strings.IMAGE_NO_FACE_ERROR
    elif error == 'Not find Mask for Object':
        comfy_error = strings.IMAGE_NO_MASK_ERROR
    elif error == 'not supported animated image':
        comfy_error = strings.NOT_SUPPORT_ANIMATED_ERROR
    else:
        comfy_error = strings.COMFY_DEFAULT_ERROR
        error_code = 4  # 表示其他错误
    return comfy_error, error_code


def call_api(url, timeout=10):
    """
    同步调用API的函数
    :param url: API地址
    :param timeout: 超时时间
    :return: API响应
    """
    try:
        response = requests.get(url, verify=False, timeout=timeout)
        response.raise_for_status()
        return response
    except requests.RequestException as e:
        error_logger.error(f"API call failed for {url}: {e}")
        raise


def record_service_error(server, success=False):
    """
    记录服务器的超时事件
    :param server:
    :param success: 如果是成功就清除记录
    :return:
    """
    if success:
        timeout_records[server] = []
        return
    now = timer()
    timeout_records[server].append(now)
    # 仅保留在时间窗口内的超时记录
    timeout_records[server] = [
        t for t in timeout_records[server] if now - t <= TIME_WINDOW
    ]


def is_server_excluded(server):
    """检查服务器是否应被剔除"""
    return len(timeout_records[server]) >= MAX_TIMEOUTS

def server_error_task_count(server):

    return len(timeout_records[server])

def get_result_from_history(address, prompt_id):
    """
    从history中去获取image的数据
    :param address:
    :param prompt_id:
    :return:
    """
    url = f"http://{address}/history/{prompt_id}"
    try:
        res = lock_call_api(url=url, lock_key=f"lock:{address}", method='get').json()
    except Exception as e:
        error_logger.error(f"Failed to get result from history for {prompt_id}: {e}")
        return {}
    history = res.get(prompt_id, {})
    outputs = list(history.get("outputs", {}).values())
    oss_results = [i for o in outputs for i in o.get('oss_results', [])]  # 对象存储结果
    texts = [i for o in outputs for i in o.get('text', [])]  # 输出个字符串内容  比如面部找点
    keypoint_data = {}
    if texts:
        point_text = texts[0]
        try:
            pattern = r'Start: \[(?P<start_point>\d+), (?P<start_point_y>\d+)\], End: \[(?P<end_point>\d+), (?P<end_point_y>\d+)\]'
            result = re.match(pattern, point_text)
            keypoint_data = {
                'start_point': [int(result.group('start_point')),
                                int(result.group('start_point_y'))],
                'end_point': [int(result.group('end_point')), int(result.group('end_point_y'))],
            }
        except Exception as e:
            if 'No valid face' in point_text:
                keypoint_data = {'start_point': [], 'end_point': []}

    return {
        'keypoint_data': keypoint_data,  # 面部找点数据
        'oss_results': oss_results,  # comfy直传图片到对象存储的结果
    }


def handle_success_task(address, prompt_id, **kwargs):
    """
    处理成功的comfy任务
    :param address:
    :param prompt_id:
    :param kwargs:
    :return:
    """
    task_id = get_prompt_taskid(prompt_id)
    start_time = kwargs.get('start_time')
    end_time = timer()
    try:
        cost_time = end_time - start_time
    except:
        cost_time = None
    task_logger.info(f"start handle success task for {prompt_id}")
    res = get_result_from_history(address, prompt_id)

    keypoint_data = res.get('keypoint_data', {})  # 面部找点数据
    result = res.get('oss_results', [])  # comfy直传图片到对象存储的结果

    if not result:
        update_info = {
            'status': 3,
            'error': "no result from history",
            'cost_time': cost_time,
        }
        sync_update_task(item_id=prompt_id, update_info=update_info)
        return
    update_info = {
        'status': 2,
        'cost_time': cost_time,
        'result': result
    }
    if keypoint_data:
        update_info.update({"keypoint_result": keypoint_data})
    sync_update_task(item_id=prompt_id, update_info=update_info)
    store_task_keypoint(task_id=task_id, keypoint=keypoint_data)  # 存面部找点数据 结果存到redis
    store_task_result(task_id=task_id, result=result)  # 生图结果
    publish_mq(task_id=task_id, result=result, keypoint_result=keypoint_data)  # 推送消息到redis
    task_logger.info(f"end handle success task for {prompt_id}")


def get_task_id(prompt_id):
    """
    通过promptid获取taskid 以有的taskid放到内存中 减少redis压力
    :param prompt_id:
    :return:
    """
    task_id_cache = prompt_taskid_cache.get(prompt_id)
    if task_id_cache:
        return task_id_cache
    task_id = get_prompt_taskid(prompt_id=prompt_id)
    if task_id:
        prompt_taskid_cache[prompt_id] = task_id
        return task_id


def publish_mq(task_id="", result=None, keypoint_result=None, status_info=None, message="", status=0, error_code=None):
    """
    推送消息到redis
    :param task_id:任务id
    :param result: 任务结果
    :param keypoint_result: 面部找点结果
    :param status_info: 任务队列状态
    :param message: 消息
    :param status:0:成功 1:失败
    :param error_code: 错误类型
    :param channel:
    :return:
    """
    if status_info is None:
        status_info = {}
    queue_status = []
    for queue_task_id, queue_info in status_info.items():
        queue_status.append({
            'task_id': queue_task_id,
            'status': queue_info.get('prompt_status', ""),
            'index': queue_info.get('index'),
        })
    db_task = sync_get_task_info(task_id)
    info = {
        "task_id": task_id,
        'result': result or None,
        'keypoint_result': keypoint_result or None,
        'queue_status': queue_status or None,
        "message": message or None,
        "status": status,
        "error_code": error_code if status else None,  # 只有在status为1的时候error_code有效
        "task_type": db_task.get('task_type') if db_task else None
    }
    task_logger.info(f'topic: {tp_task_status}, message: {info}')
    mq_redis.publish(tp_task_status, json.dumps(info))
