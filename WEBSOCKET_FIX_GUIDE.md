# WebSocket消息接收问题解决方案

## 问题描述
WebSocket监控程序只能接收到pong消息，但接收不到其他业务消息（如execution_start、status、execution_error等）。

## 问题分析

### 1. 根本原因
- **Ping/Pong机制干扰**：WebSocket的自动ping/pong机制可能与业务消息处理产生冲突
- **消息过滤问题**：代码可能错误地过滤了某些类型的消息
- **JSON解析错误**：pong帧被错误地当作JSON消息处理
- **连接配置问题**：WebSocket连接参数可能不适合ComfyUI的消息模式

### 2. 具体问题点
- `ping_interval=10`设置过于频繁，可能干扰正常消息
- 缺少对不同消息类型的详细日志记录
- 错误处理不够完善，可能掩盖了真实问题

## 解决方案

### 1. 改进的代码修改

已对 `scripts/task_monitor.py` 进行以下改进：

#### A. WebSocket连接配置优化
```python
async with websockets.connect(
    ws_url, 
    ping_interval=20,  # 增加ping间隔，减少干扰
    ping_timeout=10, 
    close_timeout=10,
    max_size=10**7,  # 增加最大消息大小限制
    compression=None  # 禁用压缩避免潜在问题
) as websocket:
```

#### B. 消息处理逻辑改进
- 添加了详细的消息类型检查和日志记录
- 改进了JSON解析错误处理
- 增加了对未处理消息类型的记录

#### C. 监控机制优化
- 改进了消息超时监控逻辑
- 添加了更详细的调试信息
- 优化了重连机制

### 2. 调试工具

#### A. 调试版本监控器
创建了 `scripts/task_monitor_debug.py`，提供：
- 详细的消息接收日志
- 所有消息类型的记录
- 完整的错误信息输出

#### B. WebSocket测试工具
创建了 `scripts/websocket_test.py`，提供：
- 独立的WebSocket连接测试
- 消息接收统计
- 连接状态监控

## 使用指南

### 1. 立即测试
```bash
# 运行WebSocket测试工具
cd /path/to/project
python scripts/websocket_test.py

# 选择选项1测试数据库中的地址
# 或选择选项2手动输入地址测试
```

### 2. 调试模式运行
```bash
# 运行调试版本的监控器
python scripts/task_monitor_debug.py
```

### 3. 部署改进版本
```bash
# 备份原文件
cp scripts/task_monitor.py scripts/task_monitor.py.backup

# 重启监控服务
sudo supervisorctl restart aiease_monitor
```

## 排查步骤

### 1. 确认WebSocket连接
```bash
# 测试基本连接
python scripts/websocket_test.py
```

### 2. 检查ComfyUI状态
```bash
# 直接访问ComfyUI API
curl http://YOUR_COMFY_ADDRESS/queue
curl http://YOUR_COMFY_ADDRESS/history
```

### 3. 查看详细日志
```bash
# 运行调试版本
python scripts/task_monitor_debug.py
```

### 4. 监控系统日志
```bash
# 查看supervisor日志
sudo tail -f /var/log/supervisor/aiease_monitor.log

# 查看应用日志
tail -f logs/task_monitor.log
```

## 可能的其他原因

### 1. ComfyUI配置问题
- ComfyUI服务器可能没有正确配置WebSocket
- 防火墙可能阻止了某些消息类型

### 2. 网络问题
- 网络延迟或丢包可能影响消息传输
- 代理或负载均衡器可能过滤了某些消息

### 3. 版本兼容性
- ComfyUI版本可能与当前代码不兼容
- WebSocket协议版本问题

## 监控和维护

### 1. 日志级别调整
在生产环境中，可以调整日志级别：
```python
# 在代码中添加
import logging
logging.getLogger('websockets').setLevel(logging.DEBUG)
```

### 2. 性能监控
- 监控消息接收频率
- 跟踪连接重连次数
- 记录处理延迟

### 3. 告警机制
- 当长时间没有消息时发送告警
- 监控连接失败率
- 跟踪消息处理错误

## 下一步行动

1. **立即执行**：运行测试工具确认问题
2. **部署修复**：应用改进的代码
3. **持续监控**：观察修复效果
4. **优化调整**：根据实际情况进一步优化

如果问题仍然存在，请提供测试工具的输出结果以便进一步诊断。
