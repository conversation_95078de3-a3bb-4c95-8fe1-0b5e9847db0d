# 动态机器上下线感知监控器

## 🎯 **功能特性**

### 1. **实时资源感知**
- ✅ **自动检测新增机器** - 数据库新增ComfyUI资源时自动上线
- ✅ **自动下线移除机器** - 数据库移除资源时自动关闭连接
- ✅ **状态变化监控** - 监控资源status字段变化
- ✅ **无需重启服务** - 动态调整，无需手动重启

### 2. **智能连接管理**
- ✅ **重连前关闭旧连接** - 解决资源泄漏问题
- ✅ **连接状态跟踪** - 实时监控连接健康状态
- ✅ **优雅关闭机制** - 支持平滑停止和重启
- ✅ **异常恢复** - 连接异常时自动重连

### 3. **详细监控日志**
- ✅ **资源变化日志** - 详细记录上下线过程
- ✅ **连接状态日志** - 监控连接健康状态
- ✅ **性能统计** - 连接数、任务数统计
- ✅ **错误追踪** - 完整的错误信息记录

## 🚀 **立即部署**

### 1. **备份现有文件**
```bash
# 备份当前监控器
cp scripts/task_monitor.py scripts/task_monitor.py.backup.$(date +%Y%m%d_%H%M%S)

# 停止现有服务
sudo supervisorctl stop aiease_monitor
```

### 2. **部署动态版本**
```bash
# 使用动态监控器
cp scripts/task_monitor_dynamic.py scripts/task_monitor.py

# 重启服务
sudo supervisorctl start aiease_monitor
```

### 3. **验证部署**
```bash
# 查看启动日志
tail -f logs/task_monitor.log

# 检查服务状态
sudo supervisorctl status aiease_monitor
```

## 📊 **监控配置**

### 1. **检查间隔设置**
```python
# 在 task_monitor_dynamic.py 中可调整
RESOURCE_CHECK_INTERVAL = 30  # 每30秒检查资源变化
RESOURCE_CACHE_TIMEOUT = 10   # 资源缓存超时时间
```

### 2. **数据库查询**
```python
# 强制从数据库获取最新数据
resource_info_list = await get_comfy_resources(use_cache=False)

# 只监控status=1的资源
addresses = {i['address'] for i in resource_info_list if i.get('status', True)}
```

## 🔍 **工作原理**

### 1. **资源变化检测**
```
数据库 → get_comfy_resources() → 地址集合比较 → 触发上下线操作
```

### 2. **新机器上线流程**
```
检测到新地址 → 添加到监控集合 → 启动WebSocket连接 → 开始消息监听
```

### 3. **机器下线流程**
```
检测到地址移除 → 从监控集合移除 → 关闭WebSocket连接 → 清理资源
```

## 📋 **日志示例**

### 1. **正常运行日志**
```
2025-07-07 15:00:00 - INFO - 🚀 Starting dynamic ComfyUI monitor...
2025-07-07 15:00:01 - INFO - 📋 Initial addresses to monitor: ['*************:8188', '*************:8188']
2025-07-07 15:00:02 - INFO - 🟢 Starting monitoring for new address: *************:8188
2025-07-07 15:00:03 - INFO - Successfully connected to *************:8188
```

### 2. **资源变化日志**
```
2025-07-07 15:05:00 - INFO - 📊 Resource change detected at 2025-07-07 15:05:00
2025-07-07 15:05:00 - INFO -    Current: ['*************:8188'] (1 total)
2025-07-07 15:05:00 - INFO -    New:     ['*************:8188', '*************:8188'] (2 total)
2025-07-07 15:05:00 - INFO - 🆕 Adding 1 new addresses:
2025-07-07 15:05:00 - INFO -    + *************:8188 (desc: GPU Server 3, tags: ['text2img'])
2025-07-07 15:05:01 - INFO - ✅ Resource change processing completed
```

### 3. **机器下线日志**
```
2025-07-07 15:10:00 - INFO - 📊 Resource change detected at 2025-07-07 15:10:00
2025-07-07 15:10:00 - INFO - ❌ Removing 1 addresses:
2025-07-07 15:10:00 - INFO -    - *************:8188
2025-07-07 15:10:00 - INFO - 🔴 Stopping monitoring for removed address: *************:8188
2025-07-07 15:10:01 - INFO - Successfully closed connection to *************:8188
2025-07-07 15:10:01 - INFO - ✅ Successfully stopped monitoring *************:8188
```

## 🧪 **测试场景**

### 1. **测试新机器上线**
```sql
-- 在数据库中添加新的ComfyUI资源
INSERT INTO server_resource (address, desc, tag, status) 
VALUES ('*************:8188', 'Test Server', '["test"]', 1);
```

**预期结果**：30秒内监控器自动检测并开始连接新地址

### 2. **测试机器下线**
```sql
-- 将机器状态设为0或删除记录
UPDATE server_resource SET status = 0 WHERE address = '*************:8188';
-- 或
DELETE FROM server_resource WHERE address = '*************:8188';
```

**预期结果**：30秒内监控器自动关闭连接并清理资源

### 3. **测试批量变更**
```sql
-- 批量添加多台机器
INSERT INTO server_resource (address, desc, tag, status) VALUES 
('*************:8188', 'Batch Server 1', '["img2img"]', 1),
('*************:8188', 'Batch Server 2', '["img2img"]', 1),
('*************:8188', 'Batch Server 3', '["img2img"]', 1);
```

**预期结果**：一次检测周期内同时上线所有新机器

## 📈 **性能优化**

### 1. **检查频率调优**
```python
# 高频场景（开发/测试）
RESOURCE_CHECK_INTERVAL = 10  # 10秒检查一次

# 生产环境（稳定）
RESOURCE_CHECK_INTERVAL = 60  # 1分钟检查一次

# 低频场景（很少变化）
RESOURCE_CHECK_INTERVAL = 300  # 5分钟检查一次
```

### 2. **数据库查询优化**
- 使用索引优化 `status` 字段查询
- 考虑添加 `updated_at` 字段进行增量检测
- 监控数据库查询性能

### 3. **内存使用优化**
- 定期清理无用的连接状态
- 限制日志输出频率
- 监控内存使用情况

## 🔧 **故障排除**

### 1. **连接问题**
```bash
# 检查网络连通性
telnet ************* 8188

# 检查ComfyUI服务状态
curl http://*************:8188/queue
```

### 2. **数据库问题**
```bash
# 检查数据库连接
python -c "from services.server_source import get_comfy_resources; import asyncio; print(asyncio.run(get_comfy_resources(use_cache=False)))"
```

### 3. **日志分析**
```bash
# 查看资源变化日志
grep "Resource change detected" logs/task_monitor.log

# 查看连接状态
grep -E "(Successfully connected|connection closed)" logs/task_monitor.log

# 查看错误信息
grep "ERROR" logs/task_monitor.log
```

## 🎉 **优势总结**

1. **🔄 自动化** - 无需手动干预，自动感知资源变化
2. **⚡ 实时性** - 30秒内响应数据库变化
3. **🛡️ 稳定性** - 完善的错误处理和恢复机制
4. **📊 可观测** - 详细的日志和状态监控
5. **🔧 可配置** - 灵活的参数调整
6. **💾 资源友好** - 及时清理无用连接，避免资源泄漏

这个动态监控器完美解决了您提出的机器上下线感知需求！
