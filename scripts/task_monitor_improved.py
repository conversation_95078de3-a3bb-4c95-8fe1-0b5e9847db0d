#!/usr/bin/env python3
"""
改进版本的WebSocket任务监控器
包含完善的连接管理和资源清理
"""

import sys
import asyncio
import time
import signal
import websockets
import json
import os

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_file_path)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from celery_manage.tasks import process_websocket_message
from conf.settings import DEFAULT_CLIENT_ID
from core.Log import ws_producer_logger, task_monitor_logger
from services.server_source import get_comfy_address_desc_map
from utils.utils import get_comfy_resources

# 全局状态管理
tc = {}  # 每个comfy最新收到message的时间
comfy_address_desc_map = {}
active_connections = {}  # 活跃的WebSocket连接
connection_tasks = {}  # 连接任务
shutdown_event = asyncio.Event()  # 关闭事件
timer = time.time


class WebSocketConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self, address):
        self.address = address
        self.ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"
        self.websocket = None
        self.is_connected = False
        self.last_message_time = timer()
        
    async def connect(self):
        """建立连接"""
        try:
            # 关闭现有连接
            await self.disconnect()
            
            task_monitor_logger.info(f"Connecting to {self.address}")
            
            self.websocket = await websockets.connect(
                self.ws_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10,
                max_size=10**7,
                compression=None
            )
            
            self.is_connected = True
            self.last_message_time = timer()
            active_connections[self.address] = self
            
            task_monitor_logger.info(f"Successfully connected to {self.address}")
            return True
            
        except Exception as e:
            task_monitor_logger.error(f"Failed to connect to {self.address}: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket and not self.websocket.closed:
            try:
                task_monitor_logger.info(f"Disconnecting from {self.address}")
                await self.websocket.close()
                await asyncio.sleep(0.1)  # 等待连接完全关闭
            except Exception as e:
                task_monitor_logger.warning(f"Error disconnecting from {self.address}: {e}")
        
        self.websocket = None
        self.is_connected = False
        active_connections.pop(self.address, None)
        tc.pop(self.address, None)
    
    async def send_message(self, message):
        """发送消息"""
        if self.websocket and self.is_connected:
            try:
                await self.websocket.send(message)
                return True
            except Exception as e:
                task_monitor_logger.error(f"Error sending message to {self.address}: {e}")
                self.is_connected = False
                return False
        return False
    
    def update_last_message_time(self):
        """更新最后消息时间"""
        self.last_message_time = timer()
        tc[self.address] = self.last_message_time


async def listen_to_websocket(manager):
    """监听WebSocket消息"""
    try:
        async for message in manager.websocket:
            manager.update_last_message_time()
            
            # 检查消息类型
            if not isinstance(message, str):
                task_monitor_logger.debug(f"Received non-string message from {manager.address}: {type(message)}")
                continue
            
            # 解析JSON消息
            try:
                parsed_message = json.loads(message)
            except json.JSONDecodeError as json_err:
                task_monitor_logger.warning(f"Failed to parse JSON from {manager.address}: {message[:100]}... Error: {json_err}")
                continue
            
            msg_type = parsed_message.get('type')
            task_monitor_logger.debug(f"Received '{msg_type}' from {manager.address}")
            
            # 处理消息
            if msg_type in ['execution_start', 'status', 'execution_error']:
                ws_producer_logger.info(f"Processing {msg_type} from {manager.address}")
                process_websocket_message.delay(parsed_message, manager.address)
            elif msg_type == 'executing':
                data = parsed_message.get('data', {})
                prompt_id = data.get('prompt_id', "")
                node = data.get('node')
                if node is None and prompt_id:
                    ws_producer_logger.info(f"Processing execution completion from {manager.address}")
                    process_websocket_message.delay(parsed_message, manager.address)
            else:
                task_monitor_logger.debug(f"Unhandled message type '{msg_type}' from {manager.address}")
                
    except websockets.exceptions.ConnectionClosed:
        task_monitor_logger.warning(f"Connection to {manager.address} was closed")
        manager.is_connected = False
    except Exception as e:
        task_monitor_logger.error(f"Error in listen_to_websocket for {manager.address}: {e}")
        manager.is_connected = False
        raise


async def message_monitor(manager, timeout=180):
    """消息超时监控"""
    check_interval = 30
    
    while not shutdown_event.is_set():
        try:
            await asyncio.wait_for(shutdown_event.wait(), timeout=check_interval)
            break  # 收到关闭信号
        except asyncio.TimeoutError:
            pass  # 继续检查
        
        current_time = timer()
        time_since_last = current_time - manager.last_message_time
        
        task_monitor_logger.debug(f"Monitor {manager.address}: {time_since_last:.1f}s since last message")
        
        if time_since_last > timeout:
            task_monitor_logger.warning(f"Timeout for {manager.address} ({time_since_last:.1f}s), forcing reconnection")
            await manager.disconnect()
            raise Exception(f"Message timeout for {manager.address}")


async def work_with_manager(address):
    """使用连接管理器的工作函数"""
    manager = WebSocketConnectionManager(address)
    
    while not shutdown_event.is_set():
        try:
            # 尝试连接
            if not await manager.connect():
                await asyncio.sleep(5)
                continue
            
            # 启动监听和监控
            listen_task = asyncio.create_task(listen_to_websocket(manager))
            monitor_task = asyncio.create_task(message_monitor(manager))
            
            try:
                await asyncio.gather(listen_task, monitor_task)
            except Exception as e:
                task_monitor_logger.warning(f"Connection error for {address}: {e}")
            finally:
                # 清理任务
                if not listen_task.done():
                    listen_task.cancel()
                if not monitor_task.done():
                    monitor_task.cancel()
                
                await manager.disconnect()
                
        except Exception as e:
            task_monitor_logger.error(f"Work error for {address}: {e}")
            await asyncio.sleep(2)


async def graceful_shutdown():
    """优雅关闭"""
    task_monitor_logger.info("Starting graceful shutdown...")
    shutdown_event.set()
    
    # 关闭所有连接
    for manager in list(active_connections.values()):
        await manager.disconnect()
    
    # 取消所有任务
    for task in list(connection_tasks.values()):
        if not task.done():
            task.cancel()
    
    task_monitor_logger.info("Graceful shutdown completed")


def signal_handler(signum, frame):
    """信号处理器"""
    task_monitor_logger.info(f"Received signal {signum}")
    asyncio.create_task(graceful_shutdown())


async def main():
    global comfy_address_desc_map
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 获取ComfyUI地址列表
        resource_info_list = await get_comfy_resources(use_cache=False)
        comfy_addr_list = list({i['address'] for i in resource_info_list})
        comfy_address_desc_map = await get_comfy_address_desc_map()
        
        if not comfy_addr_list:
            task_monitor_logger.error("No ComfyUI addresses found!")
            return
        
        task_monitor_logger.info(f"Starting improved monitor for {len(comfy_addr_list)} addresses")
        
        # 创建工作任务
        tasks = []
        for address in comfy_addr_list:
            task = asyncio.create_task(work_with_manager(address))
            connection_tasks[address] = task
            tasks.append(task)
        
        # 等待任务完成或关闭信号
        await asyncio.gather(*tasks, return_exceptions=True)
        
    except Exception as e:
        task_monitor_logger.error(f"Error in main: {e}")
    finally:
        await graceful_shutdown()


if __name__ == "__main__":
    task_monitor_logger.info("Starting improved WebSocket task monitor")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        task_monitor_logger.info("Received keyboard interrupt")
    except Exception as e:
        task_monitor_logger.error(f"Fatal error: {e}")
    finally:
        task_monitor_logger.info("Task monitor stopped")
