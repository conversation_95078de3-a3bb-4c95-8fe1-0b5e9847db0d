#!/usr/bin/env python3
"""
动态监控器测试脚本
用于测试机器上下线感知功能
"""

import sys
import asyncio
import time
import os

# 添加项目路径
current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_file_path)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from services.server_source import get_comfy_resources, get_comfy_address_desc_map
from databases.mongo import async_db as db
from core.Log import task_monitor_logger


async def get_current_resources():
    """获取当前资源状态"""
    try:
        resources = await get_comfy_resources(use_cache=False)
        addresses = {r['address'] for r in resources if r.get('status', True)}
        return resources, addresses
    except Exception as e:
        print(f"Error getting resources: {e}")
        return [], set()


async def add_test_resource(address, desc="Test Server", tag=None):
    """添加测试资源"""
    if tag is None:
        tag = ["test"]
    
    try:
        await db.server_resource.insert_one({
            "address": address,
            "desc": desc,
            "tag": tag,
            "status": 1
        })
        print(f"✅ Added test resource: {address}")
        return True
    except Exception as e:
        print(f"❌ Failed to add resource {address}: {e}")
        return False


async def remove_test_resource(address):
    """移除测试资源"""
    try:
        result = await db.server_resource.delete_one({"address": address})
        if result.deleted_count > 0:
            print(f"✅ Removed test resource: {address}")
            return True
        else:
            print(f"⚠️  Resource not found: {address}")
            return False
    except Exception as e:
        print(f"❌ Failed to remove resource {address}: {e}")
        return False


async def update_resource_status(address, status):
    """更新资源状态"""
    try:
        result = await db.server_resource.update_one(
            {"address": address},
            {"$set": {"status": status}}
        )
        if result.modified_count > 0:
            status_text = "enabled" if status else "disabled"
            print(f"✅ Updated resource {address} status to {status_text}")
            return True
        else:
            print(f"⚠️  Resource not found or no change: {address}")
            return False
    except Exception as e:
        print(f"❌ Failed to update resource {address}: {e}")
        return False


async def show_current_status():
    """显示当前资源状态"""
    resources, addresses = await get_current_resources()
    
    print("\n" + "="*60)
    print("📊 Current Resource Status")
    print("="*60)
    
    if not resources:
        print("❌ No resources found")
        return
    
    print(f"Total resources: {len(resources)}")
    print(f"Active addresses: {len(addresses)}")
    print()
    
    for resource in resources:
        address = resource['address']
        desc = resource.get('desc', 'N/A')
        tag = resource.get('tag', [])
        status = resource.get('status', True)
        status_icon = "🟢" if status else "🔴"
        
        print(f"{status_icon} {address}")
        print(f"   Description: {desc}")
        print(f"   Tags: {tag}")
        print(f"   Status: {'Active' if status else 'Inactive'}")
        print()


async def test_add_remove_cycle():
    """测试添加和移除资源的完整周期"""
    test_address = "192.168.100.999:8188"
    
    print("\n🧪 Starting Add/Remove Test Cycle")
    print("-" * 40)
    
    # 1. 显示初始状态
    print("1️⃣ Initial state:")
    await show_current_status()
    
    # 2. 添加测试资源
    print("2️⃣ Adding test resource...")
    await add_test_resource(test_address, "Dynamic Test Server", ["test", "dynamic"])
    
    # 等待一段时间让监控器检测到变化
    print("⏳ Waiting 35 seconds for monitor to detect change...")
    await asyncio.sleep(35)
    
    # 3. 显示添加后状态
    print("3️⃣ After adding:")
    await show_current_status()
    
    # 4. 禁用资源（设置status=0）
    print("4️⃣ Disabling test resource...")
    await update_resource_status(test_address, 0)
    
    # 等待监控器检测到变化
    print("⏳ Waiting 35 seconds for monitor to detect status change...")
    await asyncio.sleep(35)
    
    # 5. 显示禁用后状态
    print("5️⃣ After disabling:")
    await show_current_status()
    
    # 6. 重新启用资源
    print("6️⃣ Re-enabling test resource...")
    await update_resource_status(test_address, 1)
    
    # 等待监控器检测到变化
    print("⏳ Waiting 35 seconds for monitor to detect re-enable...")
    await asyncio.sleep(35)
    
    # 7. 显示重新启用后状态
    print("7️⃣ After re-enabling:")
    await show_current_status()
    
    # 8. 最终清理
    print("8️⃣ Cleaning up test resource...")
    await remove_test_resource(test_address)
    
    # 等待监控器检测到变化
    print("⏳ Waiting 35 seconds for monitor to detect removal...")
    await asyncio.sleep(35)
    
    # 9. 显示最终状态
    print("9️⃣ Final state:")
    await show_current_status()
    
    print("\n✅ Add/Remove Test Cycle Completed!")


async def test_batch_operations():
    """测试批量操作"""
    test_addresses = [
        "***************:8188",
        "***************:8188", 
        "***************:8188"
    ]
    
    print("\n🧪 Starting Batch Operations Test")
    print("-" * 40)
    
    # 1. 批量添加
    print("1️⃣ Batch adding 3 test resources...")
    for i, address in enumerate(test_addresses, 1):
        await add_test_resource(address, f"Batch Test Server {i}", ["batch", "test"])
    
    print("⏳ Waiting 35 seconds for monitor to detect batch addition...")
    await asyncio.sleep(35)
    
    print("2️⃣ After batch addition:")
    await show_current_status()
    
    # 2. 批量移除
    print("3️⃣ Batch removing test resources...")
    for address in test_addresses:
        await remove_test_resource(address)
    
    print("⏳ Waiting 35 seconds for monitor to detect batch removal...")
    await asyncio.sleep(35)
    
    print("4️⃣ After batch removal:")
    await show_current_status()
    
    print("\n✅ Batch Operations Test Completed!")


async def monitor_changes_realtime():
    """实时监控资源变化"""
    print("\n🔍 Real-time Resource Change Monitor")
    print("-" * 40)
    print("Monitoring for 5 minutes. Make changes in database to see detection...")
    print("Press Ctrl+C to stop")
    
    previous_addresses = set()
    
    try:
        for i in range(10):  # 监控5分钟（10次 * 30秒）
            resources, current_addresses = await get_current_resources()
            
            if current_addresses != previous_addresses:
                print(f"\n🔄 Change detected at {time.strftime('%H:%M:%S')}:")
                
                added = current_addresses - previous_addresses
                removed = previous_addresses - current_addresses
                
                if added:
                    print(f"   ➕ Added: {sorted(added)}")
                if removed:
                    print(f"   ➖ Removed: {sorted(removed)}")
                
                print(f"   📊 Total: {len(current_addresses)} addresses")
                previous_addresses = current_addresses
            else:
                print(f"⏰ {time.strftime('%H:%M:%S')} - No changes ({len(current_addresses)} addresses)")
            
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        print("\n⏹️  Monitoring stopped by user")


async def cleanup_test_resources():
    """清理所有测试资源"""
    print("\n🧹 Cleaning up test resources...")
    
    try:
        # 删除所有包含"test"标签的资源
        result = await db.server_resource.delete_many({
            "tag": {"$in": ["test"]}
        })
        
        # 删除所有描述包含"Test"的资源
        result2 = await db.server_resource.delete_many({
            "desc": {"$regex": "Test", "$options": "i"}
        })
        
        total_deleted = result.deleted_count + result2.deleted_count
        print(f"✅ Cleaned up {total_deleted} test resources")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")


async def main():
    """主函数"""
    print("🎯 Dynamic Monitor Test Suite")
    print("=" * 50)
    
    while True:
        print("\nSelect test option:")
        print("1. Show current resource status")
        print("2. Test add/remove cycle")
        print("3. Test batch operations")
        print("4. Real-time change monitoring")
        print("5. Cleanup test resources")
        print("0. Exit")
        
        try:
            choice = input("\nEnter your choice (0-5): ").strip()
            
            if choice == "0":
                print("👋 Goodbye!")
                break
            elif choice == "1":
                await show_current_status()
            elif choice == "2":
                await test_add_remove_cycle()
            elif choice == "3":
                await test_batch_operations()
            elif choice == "4":
                await monitor_changes_realtime()
            elif choice == "5":
                await cleanup_test_resources()
            else:
                print("❌ Invalid choice. Please enter 0-5.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test suite interrupted")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
