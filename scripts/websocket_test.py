#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于诊断WebSocket连接问题和消息接收情况
"""

import asyncio
import websockets
import json
import time
import sys
import os

# 添加项目路径
current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_file_path)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from conf.settings import DEFAULT_CLIENT_ID


async def test_websocket_connection(address, duration=60):
    """
    测试WebSocket连接并记录所有接收到的消息
    
    Args:
        address: ComfyUI服务器地址
        duration: 测试持续时间（秒）
    """
    ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"
    print(f"Testing WebSocket connection to: {ws_url}")
    print(f"Test duration: {duration} seconds")
    print("-" * 50)
    
    message_count = 0
    start_time = time.time()
    
    try:
        async with websockets.connect(
            ws_url,
            ping_interval=20,
            ping_timeout=10,
            close_timeout=10,
            max_size=10**7,
            compression=None
        ) as websocket:
            print(f"✅ Successfully connected to {address}")
            print(f"WebSocket state: {websocket.state}")
            print("-" * 50)
            
            # 设置超时
            timeout_task = asyncio.create_task(asyncio.sleep(duration))
            
            try:
                while not timeout_task.done():
                    try:
                        # 等待消息，但有超时
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        message_count += 1
                        current_time = time.time()
                        elapsed = current_time - start_time
                        
                        print(f"\n📨 Message #{message_count} (at {elapsed:.1f}s):")
                        print(f"   Type: {type(message)}")
                        print(f"   Length: {len(message) if hasattr(message, '__len__') else 'N/A'}")
                        
                        if isinstance(message, str):
                            try:
                                parsed = json.loads(message)
                                msg_type = parsed.get('type', 'unknown')
                                print(f"   JSON Type: {msg_type}")
                                
                                # 显示消息内容（截断长消息）
                                if len(message) > 200:
                                    print(f"   Content: {message[:200]}...")
                                else:
                                    print(f"   Content: {message}")
                                    
                            except json.JSONDecodeError:
                                print(f"   Raw content: {message[:100]}...")
                        else:
                            print(f"   Binary/Other: {str(message)[:100]}...")
                            
                    except asyncio.TimeoutError:
                        # 5秒内没有消息，继续等待
                        current_time = time.time()
                        elapsed = current_time - start_time
                        print(f"⏰ No message for 5s (total elapsed: {elapsed:.1f}s)")
                        
                        if elapsed >= duration:
                            break
                            
            except asyncio.CancelledError:
                pass
            finally:
                timeout_task.cancel()
                
    except websockets.exceptions.ConnectionClosedError as e:
        print(f"❌ Connection closed: {e}")
    except websockets.exceptions.InvalidURI as e:
        print(f"❌ Invalid URI: {e}")
    except Exception as e:
        print(f"❌ Connection error: {e}")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print("\n" + "=" * 50)
    print(f"📊 Test Summary:")
    print(f"   Total time: {total_time:.1f} seconds")
    print(f"   Messages received: {message_count}")
    print(f"   Average rate: {message_count/total_time:.2f} messages/second")
    print("=" * 50)


async def test_multiple_addresses():
    """
    测试多个ComfyUI地址
    """
    try:
        from utils.utils import get_comfy_resources
        resource_info_list = await get_comfy_resources(use_cache=False)
        comfy_addr_list = list({i['address'] for i in resource_info_list})
        
        if not comfy_addr_list:
            print("❌ No ComfyUI addresses found in database")
            return
            
        print(f"Found {len(comfy_addr_list)} ComfyUI addresses:")
        for i, addr in enumerate(comfy_addr_list, 1):
            print(f"  {i}. {addr}")
        
        # 测试第一个地址
        if comfy_addr_list:
            print(f"\n🧪 Testing first address: {comfy_addr_list[0]}")
            await test_websocket_connection(comfy_addr_list[0], duration=30)
            
    except Exception as e:
        print(f"❌ Error getting ComfyUI addresses: {e}")


async def manual_test():
    """
    手动指定地址进行测试
    """
    print("Manual WebSocket Test")
    print("Enter ComfyUI address (e.g., *************:8188):")
    address = input().strip()
    
    if not address:
        print("❌ No address provided")
        return
        
    await test_websocket_connection(address, duration=60)


if __name__ == "__main__":
    print("WebSocket Connection Tester")
    print("1. Test addresses from database")
    print("2. Manual address test")
    
    choice = input("Choose option (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_multiple_addresses())
    elif choice == "2":
        asyncio.run(manual_test())
    else:
        print("Invalid choice")
