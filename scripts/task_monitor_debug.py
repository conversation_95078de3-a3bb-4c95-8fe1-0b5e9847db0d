import sys
import asyncio
import time

import websockets
import json
import os

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
# 获取当前脚本所在的目录
current_dir = os.path.dirname(current_file_path)
# 获取当前脚本所在目录的上一级目录
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from celery_manage.tasks import process_websocket_message
from conf.settings import DEFAULT_CLIENT_ID
from core.Log import ws_producer_logger, task_monitor_logger
from services.server_source import get_comfy_address_desc_map

from utils.utils import get_comfy_resources

tc = {}  # 配置 {address: time.time()} 每个comfy最新收到message的时间
comfy_address_desc_map = {}
timer = time.time


async def debug_listen_to_websocket(websocket, address):
    """
    调试版本的WebSocket监听器 - 记录所有接收到的消息
    """
    message_count = 0
    try:
        async for message in websocket:
            message_count += 1
            tc[address] = timer()
            
            # 记录所有消息的基本信息
            print(f"[DEBUG] Message #{message_count} from {address}")
            print(f"[DEBUG] Message type: {type(message)}")
            print(f"[DEBUG] Message length: {len(message) if hasattr(message, '__len__') else 'N/A'}")
            
            # 检查消息类型
            if isinstance(message, bytes):
                print(f"[DEBUG] Received bytes message: {message[:100]}...")
                continue
            elif not isinstance(message, str):
                print(f"[DEBUG] Received non-string message: {message}")
                continue
            
            # 尝试解析JSON
            try:
                parsed_message = json.loads(message)
                msg_type = parsed_message.get('type', 'unknown')
                print(f"[DEBUG] Parsed JSON message type: {msg_type}")
                print(f"[DEBUG] Full message: {json.dumps(parsed_message, indent=2)}")
                
                # 处理消息
                if msg_type in ['execution_start', 'status', 'execution_error']:
                    print(f"[DEBUG] Processing message type: {msg_type}")
                    ws_producer_logger.info(f"producing: {parsed_message}")
                    process_websocket_message.delay(parsed_message, address)
                elif msg_type == 'executing':
                    data = parsed_message.get('data', {})
                    prompt_id = data.get('prompt_id', "")
                    node = data.get('node')
                    print(f"[DEBUG] Executing message - node: {node}, prompt_id: {prompt_id}")
                    if node is None and prompt_id:
                        print(f"[DEBUG] Processing executing message for completion")
                        ws_producer_logger.info(f"producing: {parsed_message}")
                        process_websocket_message.delay(parsed_message, address)
                else:
                    print(f"[DEBUG] Unhandled message type: {msg_type}")
                    
            except json.JSONDecodeError as e:
                print(f"[DEBUG] JSON decode error: {e}")
                print(f"[DEBUG] Raw message: {message[:200]}...")
                
    except Exception as e:
        print(f"[DEBUG] Exception in listen_to_websocket: {e}")
        task_monitor_logger.error(f"WebSocket connection closed {e}")
        raise e


async def debug_work(address):
    """
    调试版本的工作函数
    """
    ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"
    print(f"[DEBUG] Starting debug connection to {ws_url}")
    
    while True:
        try:
            print(f"[DEBUG] Attempting to connect to {ws_url}")
            async with websockets.connect(
                ws_url, 
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10,
                max_size=10**7,
                compression=None
            ) as websocket:
                print(f"[DEBUG] Successfully connected to {address}")
                await debug_listen_to_websocket(websocket, address)
                
        except Exception as e:
            print(f"[DEBUG] Connection error: {e}")
            await asyncio.sleep(2)


async def debug_main():
    """
    调试版本的主函数 - 只连接一个地址进行测试
    """
    global comfy_address_desc_map
    resource_info_list = await get_comfy_resources(use_cache=False)
    comfy_addr_list = list({i['address'] for i in resource_info_list})
    comfy_address_desc_map = await get_comfy_address_desc_map()
    
    if not comfy_addr_list:
        print("[DEBUG] No ComfyUI addresses found!")
        return
    
    # 只测试第一个地址
    test_address = comfy_addr_list[0]
    print(f"[DEBUG] Testing with address: {test_address}")
    
    await debug_work(test_address)


if __name__ == "__main__":
    print("[DEBUG] Starting debug version of task monitor")
    asyncio.run(debug_main())
