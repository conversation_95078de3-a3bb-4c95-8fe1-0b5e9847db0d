#!/usr/bin/env python3
"""
动态机器上下线感知的WebSocket任务监控器
支持实时检测数据库中ComfyUI资源的变化，自动上线新机器和下线移除的机器
"""

import sys
import asyncio
import time
import websockets
import json
import os

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_file_path)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from celery_manage.tasks import process_websocket_message
from conf.settings import DEFAULT_CLIENT_ID
from core.Log import ws_producer_logger, task_monitor_logger
from services.server_source import get_comfy_address_desc_map
from utils.utils import get_comfy_resources

# 全局状态管理
tc = {}  # 每个comfy最新收到message的时间
comfy_address_desc_map = {}
active_connections = {}  # 活跃的WebSocket连接 {address: websocket}
connection_tasks = {}  # 连接任务 {address: task}
current_addresses = set()  # 当前监控的地址集合
timer = time.time

# 动态监控配置
RESOURCE_CHECK_INTERVAL = 30  # 每30秒检查一次资源变化
RESOURCE_CACHE_TIMEOUT = 10   # 资源缓存超时时间（秒）


def is_websocket_closed(websocket):
    """安全地检查WebSocket是否已关闭"""
    try:
        if hasattr(websocket, 'state'):
            return websocket.state in [websockets.protocol.State.CLOSED, websockets.protocol.State.CLOSING]
        elif hasattr(websocket, 'closed'):
            return websocket.closed
        else:
            return False
    except Exception:
        return True


async def safe_close_websocket(websocket, address):
    """安全地关闭WebSocket连接"""
    try:
        if websocket and not is_websocket_closed(websocket):
            await websocket.close()
            task_monitor_logger.info(f"Successfully closed connection to {address}")
    except Exception as e:
        task_monitor_logger.warning(f"Error closing connection to {address}: {e}")


async def close_existing_connection(address):
    """关闭指定地址的现有连接"""
    if address in active_connections:
        websocket = active_connections.pop(address, None)
        if websocket:
            await safe_close_websocket(websocket, address)
            await asyncio.sleep(0.5)

    # 取消连接任务
    if address in connection_tasks:
        task = connection_tasks.pop(address, None)
        if task and not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

    # 清理状态
    tc.pop(address, None)


async def work(address):
    """单个地址的WebSocket连接工作函数"""
    ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"

    while address in current_addresses:  # 只要地址还在监控列表中就继续
        websocket = None
        try:
            # 在重连之前，先关闭现有连接
            if address in active_connections:
                old_websocket = active_connections.pop(address, None)
                if old_websocket:
                    await safe_close_websocket(old_websocket, address)

            task_monitor_logger.info(
                f"start connecting to {comfy_address_desc_map.get(address, '')} websocket at {ws_url}")

            # 配置WebSocket连接参数
            websocket = await websockets.connect(
                ws_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10,
                max_size=10**7,
                compression=None
            )

            # 存储活跃连接
            active_connections[address] = websocket
            task_monitor_logger.info(f"Successfully connected to {address}")

            # 启动监听和监控任务
            listen_task = asyncio.create_task(listen_to_websocket(websocket, address))
            monitor_task = asyncio.create_task(message_monitor(address, timeout=180))

            # 等待任务完成或异常
            try:
                await asyncio.gather(listen_task, monitor_task)
            except Exception as e:
                # 取消未完成的任务
                if not listen_task.done():
                    listen_task.cancel()
                if not monitor_task.done():
                    monitor_task.cancel()
                raise e

        except websockets.exceptions.ConnectionClosedError as e:
            task_monitor_logger.warning(f"WebSocket connection closed for {ws_url}: {e}")
            await cleanup_connection(address, websocket)
            await asyncio.sleep(2)
        except websockets.exceptions.InvalidURI as e:
            task_monitor_logger.error(f"Invalid WebSocket URI {ws_url}: {e}")
            await cleanup_connection(address, websocket)
            await asyncio.sleep(5)
        except Exception as e:
            task_monitor_logger.error(f"Exception while connecting to websocket {ws_url}, due to {e}")
            await cleanup_connection(address, websocket)
            await asyncio.sleep(1)

    # 地址不在监控列表中，清理连接
    task_monitor_logger.info(f"Address {address} removed from monitoring, cleaning up connection")
    await cleanup_connection(address, websocket)


async def cleanup_connection(address, websocket):
    """清理连接资源"""
    if websocket:
        await safe_close_websocket(websocket, address)

    # 清理状态
    active_connections.pop(address, None)
    tc.pop(address, None)


async def listen_to_websocket(websocket, address):
    """监听websocket消息"""
    try:
        async for message in websocket:
            tc[address] = timer()

            # 检查消息类型，过滤掉非文本消息（如pong帧）
            if not isinstance(message, str):
                task_monitor_logger.debug(f"Received non-string message from {address}: {type(message)}")
                continue

            # 尝试解析JSON消息
            try:
                parsed_message = json.loads(message)
            except json.JSONDecodeError as json_err:
                task_monitor_logger.warning(f"Failed to parse JSON message from {address}: {message[:100]}... Error: {json_err}")
                continue

            msg_type = parsed_message.get('type')
            task_monitor_logger.debug(f"Received message type '{msg_type}' from {address}")

            # 处理特定类型的消息
            if msg_type in ['execution_start', 'status', 'execution_error']:
                ws_producer_logger.info(f"producing: {parsed_message}")
                process_websocket_message.delay(parsed_message, address)
            elif msg_type == 'executing':
                data = parsed_message['data']
                prompt_data = parsed_message.get('data', {})
                prompt_id = prompt_data.get('prompt_id', "")
                if data['node'] is None and data['prompt_id'] == prompt_id:
                    ws_producer_logger.info(f"producing: {parsed_message}")
                    process_websocket_message.delay(parsed_message, address)
            else:
                # 记录未处理的消息类型，用于调试
                task_monitor_logger.debug(f"Unhandled message type '{msg_type}' from {address}: {parsed_message}")

    except Exception as e:
        task_monitor_logger.error(f"WebSocket connection closed {e}")
        raise e


async def message_monitor(address, timeout=180):
    """消息监控"""
    tc[address] = timer()
    check_interval = 30

    while address in current_addresses:  # 只要地址还在监控列表中就继续
        await asyncio.sleep(check_interval)
        current_time = timer()
        last_message_time = tc.get(address, current_time)
        time_since_last_message = current_time - last_message_time

        task_monitor_logger.debug(f"Message monitor for {address}: {time_since_last_message:.1f}s since last message")

        if time_since_last_message > timeout:
            task_monitor_logger.warning(f"No message received from {address} for {time_since_last_message:.1f} seconds, closing connection...")

            # 主动关闭连接
            if address in active_connections:
                websocket = active_connections[address]
                await safe_close_websocket(websocket, address)

            # 清理状态
            tc.pop(address, None)
            active_connections.pop(address, None)

            raise Exception(f"{address} after {timeout} seconds no message received")


async def get_current_comfy_addresses():
    """获取当前数据库中的ComfyUI地址列表"""
    try:
        # 强制从数据库获取最新数据，不使用缓存
        resource_info_list = await get_comfy_resources(use_cache=False)
        addresses = {i['address'] for i in resource_info_list if i.get('status', True)}
        return addresses
    except Exception as e:
        task_monitor_logger.error(f"Error getting comfy addresses: {e}")
        return set()


async def start_monitoring_address(address):
    """开始监控新地址"""
    if address not in connection_tasks:
        task_monitor_logger.info(f"🟢 Starting monitoring for new address: {address}")
        task = asyncio.create_task(work(address))
        connection_tasks[address] = task
        return task
    return None


async def stop_monitoring_address(address):
    """停止监控地址"""
    task_monitor_logger.info(f"🔴 Stopping monitoring for removed address: {address}")

    # 从当前地址集合中移除
    current_addresses.discard(address)

    # 关闭连接
    await close_existing_connection(address)

    task_monitor_logger.info(f"✅ Successfully stopped monitoring {address}")


async def resource_change_monitor():
    """资源变化监控器 - 定期检查数据库中的资源变化"""
    global current_addresses, comfy_address_desc_map

    last_check_time = 0

    while True:
        try:
            await asyncio.sleep(RESOURCE_CHECK_INTERVAL)

            current_time = time.time()

            # 获取最新的地址列表和详细信息
            try:
                resource_info_list = await get_comfy_resources(use_cache=False)
                new_addresses = {i['address'] for i in resource_info_list if i.get('status', True)}

                # 构建地址到资源信息的映射
                address_info_map = {i['address']: i for i in resource_info_list if i.get('status', True)}

            except Exception as e:
                task_monitor_logger.error(f"Failed to get resource info: {e}")
                continue

            if new_addresses != current_addresses:
                task_monitor_logger.info(f"📊 Resource change detected at {time.strftime('%Y-%m-%d %H:%M:%S')}")
                task_monitor_logger.info(f"   Current: {sorted(current_addresses)} ({len(current_addresses)} total)")
                task_monitor_logger.info(f"   New:     {sorted(new_addresses)} ({len(new_addresses)} total)")

                # 找出新增的地址
                added_addresses = new_addresses - current_addresses
                # 找出移除的地址
                removed_addresses = current_addresses - new_addresses

                # 处理新增的地址
                if added_addresses:
                    task_monitor_logger.info(f"🆕 Adding {len(added_addresses)} new addresses:")
                    for address in added_addresses:
                        info = address_info_map.get(address, {})
                        desc = info.get('desc', 'Unknown')
                        tag = info.get('tag', [])
                        task_monitor_logger.info(f"   + {address} (desc: {desc}, tags: {tag})")
                        current_addresses.add(address)
                        await start_monitoring_address(address)

                # 处理移除的地址
                if removed_addresses:
                    task_monitor_logger.info(f"❌ Removing {len(removed_addresses)} addresses:")
                    for address in removed_addresses:
                        task_monitor_logger.info(f"   - {address}")
                        await stop_monitoring_address(address)

                # 更新地址描述映射
                try:
                    comfy_address_desc_map = await get_comfy_address_desc_map()
                    task_monitor_logger.debug(f"Updated address description mapping")
                except Exception as e:
                    task_monitor_logger.warning(f"Failed to update address desc map: {e}")

                task_monitor_logger.info(f"✅ Resource change processing completed")
                task_monitor_logger.info(f"   Now monitoring: {len(current_addresses)} addresses")

                # 显示当前监控状态
                active_count = len(active_connections)
                task_count = len(connection_tasks)
                task_monitor_logger.info(f"   Active connections: {active_count}, Running tasks: {task_count}")

            else:
                # 定期显示状态摘要（每5分钟）
                if current_time - last_check_time > 300:  # 5分钟
                    active_count = len(active_connections)
                    task_count = len(connection_tasks)
                    task_monitor_logger.info(f"📊 Status: monitoring {len(current_addresses)} addresses, "
                                           f"{active_count} active connections, {task_count} tasks")
                    last_check_time = current_time
                else:
                    task_monitor_logger.debug(f"📊 No resource changes detected ({len(current_addresses)} addresses)")

        except Exception as e:
            task_monitor_logger.error(f"Error in resource change monitor: {e}")
            await asyncio.sleep(5)  # 出错时短暂等待


async def connection_health_monitor():
    """连接健康监控"""
    while True:
        try:
            await asyncio.sleep(60)

            current_time = timer()
            for address, websocket in list(active_connections.items()):
                try:
                    if is_websocket_closed(websocket):
                        task_monitor_logger.warning(f"Detected closed connection for {address}, cleaning up")
                        active_connections.pop(address, None)
                        tc.pop(address, None)
                    else:
                        last_message_time = tc.get(address, current_time)
                        if current_time - last_message_time > 300:
                            task_monitor_logger.warning(f"Connection to {address} appears stale")

                except Exception as e:
                    task_monitor_logger.error(f"Error checking connection health for {address}: {e}")
                    active_connections.pop(address, None)
                    tc.pop(address, None)

        except Exception as e:
            task_monitor_logger.error(f"Error in connection health monitor: {e}")


async def graceful_shutdown():
    """优雅关闭所有连接"""
    task_monitor_logger.info("Starting graceful shutdown...")

    # 停止所有地址的监控
    for address in list(current_addresses):
        await stop_monitoring_address(address)

    # 清理状态
    current_addresses.clear()
    active_connections.clear()
    tc.clear()
    connection_tasks.clear()

    task_monitor_logger.info("Graceful shutdown completed")


async def main():
    global current_addresses, comfy_address_desc_map

    try:
        # 初始化：获取当前的ComfyUI地址列表
        task_monitor_logger.info("🚀 Starting dynamic ComfyUI monitor...")

        current_addresses = await get_current_comfy_addresses()
        comfy_address_desc_map = await get_comfy_address_desc_map()

        if not current_addresses:
            task_monitor_logger.warning("⚠️  No ComfyUI addresses found in database!")
        else:
            task_monitor_logger.info(f"📋 Initial addresses to monitor: {sorted(current_addresses)}")

        # 启动初始监控任务
        for address in current_addresses:
            await start_monitoring_address(address)

        # 启动资源变化监控器
        resource_monitor_task = asyncio.create_task(resource_change_monitor())

        # 启动连接健康监控器
        health_monitor_task = asyncio.create_task(connection_health_monitor())

        # 等待所有任务
        all_tasks = list(connection_tasks.values()) + [resource_monitor_task, health_monitor_task]
        await asyncio.gather(*all_tasks, return_exceptions=True)

    except KeyboardInterrupt:
        task_monitor_logger.info("Received shutdown signal")
        await graceful_shutdown()
    except Exception as e:
        task_monitor_logger.error(f"Error in main: {e}")
        await graceful_shutdown()
        raise


if __name__ == "__main__":
    task_monitor_logger.info("🎯 Starting Dynamic ComfyUI WebSocket Monitor")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        task_monitor_logger.info("Received keyboard interrupt")
    except Exception as e:
        task_monitor_logger.error(f"Fatal error: {e}")
    finally:
        task_monitor_logger.info("Dynamic monitor stopped")
