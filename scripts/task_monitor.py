import sys
import asyncio
import time

import websockets
import json
import os

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
# 获取当前脚本所在的目录
current_dir = os.path.dirname(current_file_path)
# 获取当前脚本所在目录的上一级目录
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from celery_manage.tasks import process_websocket_message
from conf.settings import DEFAULT_CLIENT_ID
from core.Log import ws_producer_logger, task_monitor_logger
from services.server_source import get_comfy_address_desc_map

from utils.utils import get_comfy_resources

tc = {}  # 配置 {address: time.time()} 每个comfy最新收到message的时间
comfy_address_desc_map = {}
timer = time.time


async def work(address):
    ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"
    while True:
        try:
            task_monitor_logger.info(
                f"start connecting to {comfy_address_desc_map.get(address, '')} websocket at {ws_url}")
            async with websockets.connect(ws_url, ping_interval=10, ping_timeout=10) as websocket:
                await asyncio.gather(
                    listen_to_websocket(websocket, address),
                    message_monitor(address, timeout=180)  # 获取message超时检测
                )

        except Exception as e:
            task_monitor_logger.error(f"Exception while connecting to websocket{ws_url}, due to {e}")
            await asyncio.sleep(1)


async def listen_to_websocket(websocket, address):
    """
    监听websocket
    :param websocket:
    :param address:
    :return:
    """

    try:
        async for message in websocket:
            tc[address] = timer()

            # 检查消息类型，过滤掉非文本消息（如pong帧）
            if not isinstance(message, str):
                task_monitor_logger.debug(f"Received non-string message from {address}: {type(message)}")
                continue

            # 尝试解析JSON消息
            try:
                parsed_message = json.loads(message)
            except json.JSONDecodeError as json_err:
                task_monitor_logger.warning(f"Failed to parse JSON message from {address}: {message[:100]}... Error: {json_err}")
                continue

            msg_type = parsed_message.get('type')
            task_monitor_logger.debug(f"Received message type '{msg_type}' from {address}")

            # 处理特定类型的消息
            if msg_type in ['execution_start', 'status', 'execution_error']:
                ws_producer_logger.info(f"producing: {parsed_message}")
                process_websocket_message.delay(parsed_message, address)
            elif msg_type == 'executing':
                data = parsed_message['data']
                prompt_data = parsed_message.get('data', {})
                prompt_id = prompt_data.get('prompt_id', "")
                if data['node'] is None and data['prompt_id'] == prompt_id:
                    ws_producer_logger.info(f"producing: {parsed_message}")
                    process_websocket_message.delay(parsed_message, address)
            else:
                # 记录未处理的消息类型，用于调试
                task_monitor_logger.debug(f"Unhandled message type '{msg_type}' from {address}: {parsed_message}")

    except Exception as e:
        task_monitor_logger.error(f"WebSocket connection closed {e}")
        raise e


async def message_monitor(address, timeout=30):
    """
    消息监控 30秒检测一次有没有新消息 没有新消息就抛异常(抛异常后会自动重新连接)
    :param address:
    :param timeout:
    :return:
    """

    tc[address] = timer()  # 初始化一个时间
    while True:
        # 如果上次接受message的时间超过timeout 就抛异常  此时websocket会重新连接
        if timer() - tc[address] > timeout:
            tc.pop(address, None)
            raise Exception(f"{address} after {timeout} seconds no message received")
        await asyncio.sleep(timeout)


async def main():
    global comfy_address_desc_map
    resource_info_list = await get_comfy_resources(use_cache=False)  # use_cache 传false 每次运行都从数据库拿最新的
    comfy_addr_list = list({i['address'] for i in resource_info_list})
    comfy_address_desc_map = await get_comfy_address_desc_map()
    tasks = [work(address) for address in comfy_addr_list]
    await asyncio.gather(*tasks)


if __name__ == "__main__":
    asyncio.run(main())
