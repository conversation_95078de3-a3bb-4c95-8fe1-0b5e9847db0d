import sys
import asyncio
import time

import websockets
import json
import os

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
# 获取当前脚本所在的目录
current_dir = os.path.dirname(current_file_path)
# 获取当前脚本所在目录的上一级目录
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from celery_manage.tasks import process_websocket_message
from conf.settings import DEFAULT_CLIENT_ID
from core.Log import ws_producer_logger, task_monitor_logger
from services.server_source import get_comfy_address_desc_map
from utils.utils import get_comfy_resources

tc = {}  # 配置 {address: time.time()} 每个comfy最新收到message的时间
comfy_address_desc_map = {}
active_connections = {}  # 存储活跃的WebSocket连接 {address: websocket}
timer = time.time


def is_websocket_closed(websocket):
    """
    安全地检查WebSocket是否已关闭
    """
    try:
        # 尝试多种方法检查连接状态
        if hasattr(websocket, 'state'):
            return websocket.state in [websockets.protocol.State.CLOSED, websockets.protocol.State.CLOSING]
        elif hasattr(websocket, 'closed'):
            return websocket.closed
        else:
            # 如果没有状态属性，假设连接是活跃的
            return False
    except Exception:
        # 如果检查失败，假设连接已关闭
        return True


async def safe_close_websocket(websocket, address):
    """
    安全地关闭WebSocket连接
    """
    try:
        if websocket and not is_websocket_closed(websocket):
            await websocket.close()
            task_monitor_logger.info(f"Successfully closed connection to {address}")
    except Exception as e:
        task_monitor_logger.warning(f"Error closing connection to {address}: {e}")


async def close_existing_connection(address):
    """
    关闭指定地址的现有连接
    """
    if address in active_connections:
        websocket = active_connections.pop(address, None)
        if websocket:
            await safe_close_websocket(websocket, address)
            # 等待连接完全关闭
            await asyncio.sleep(0.5)
    
    # 清理状态
    tc.pop(address, None)


async def work(address):
    ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"
    
    while True:
        websocket = None
        try:
            # 在重连之前，先关闭现有连接
            await close_existing_connection(address)
            
            task_monitor_logger.info(
                f"start connecting to {comfy_address_desc_map.get(address, '')} websocket at {ws_url}")
            
            # 配置WebSocket连接参数
            websocket = await websockets.connect(
                ws_url, 
                ping_interval=20,  # 增加ping间隔，减少ping/pong频率
                ping_timeout=10, 
                close_timeout=10,
                max_size=10**7,  # 增加最大消息大小限制
                compression=None  # 禁用压缩以避免潜在问题
            )
            
            # 存储活跃连接
            active_connections[address] = websocket
            task_monitor_logger.info(f"Successfully connected to {address}")
            
            # 启动监听和监控任务
            listen_task = asyncio.create_task(listen_to_websocket(websocket, address))
            monitor_task = asyncio.create_task(message_monitor(address, timeout=180))
            
            # 等待任务完成或异常
            try:
                await asyncio.gather(listen_task, monitor_task)
            except Exception as e:
                # 取消未完成的任务
                if not listen_task.done():
                    listen_task.cancel()
                if not monitor_task.done():
                    monitor_task.cancel()
                raise e

        except websockets.exceptions.ConnectionClosedError as e:
            task_monitor_logger.warning(f"WebSocket connection closed for {ws_url}: {e}")
            await cleanup_connection(address, websocket)
            await asyncio.sleep(2)
        except websockets.exceptions.InvalidURI as e:
            task_monitor_logger.error(f"Invalid WebSocket URI {ws_url}: {e}")
            await cleanup_connection(address, websocket)
            await asyncio.sleep(5)
        except Exception as e:
            task_monitor_logger.error(f"Exception while connecting to websocket {ws_url}, due to {e}")
            await cleanup_connection(address, websocket)
            await asyncio.sleep(1)


async def cleanup_connection(address, websocket):
    """
    清理连接资源
    """
    if websocket:
        await safe_close_websocket(websocket, address)
    
    # 清理状态
    active_connections.pop(address, None)
    tc.pop(address, None)


async def listen_to_websocket(websocket, address):
    """
    监听websocket
    """
    try:
        async for message in websocket:
            tc[address] = timer()
            
            # 检查消息类型，过滤掉非文本消息（如pong帧）
            if not isinstance(message, str):
                task_monitor_logger.debug(f"Received non-string message from {address}: {type(message)}")
                continue
            
            # 尝试解析JSON消息
            try:
                parsed_message = json.loads(message)
            except json.JSONDecodeError as json_err:
                task_monitor_logger.warning(f"Failed to parse JSON message from {address}: {message[:100]}... Error: {json_err}")
                continue
            
            msg_type = parsed_message.get('type')
            task_monitor_logger.debug(f"Received message type '{msg_type}' from {address}")
            
            # 处理特定类型的消息
            if msg_type in ['execution_start', 'status', 'execution_error']:
                ws_producer_logger.info(f"producing: {parsed_message}")
                process_websocket_message.delay(parsed_message, address)
            elif msg_type == 'executing':
                data = parsed_message['data']
                prompt_data = parsed_message.get('data', {})
                prompt_id = prompt_data.get('prompt_id', "")
                if data['node'] is None and data['prompt_id'] == prompt_id:
                    ws_producer_logger.info(f"producing: {parsed_message}")
                    process_websocket_message.delay(parsed_message, address)
            else:
                # 记录未处理的消息类型，用于调试
                task_monitor_logger.debug(f"Unhandled message type '{msg_type}' from {address}: {parsed_message}")

    except Exception as e:
        task_monitor_logger.error(f"WebSocket connection closed {e}")
        raise e


async def message_monitor(address, timeout=180):
    """
    消息监控 检测有没有新消息 没有新消息就主动关闭连接并抛异常
    """
    tc[address] = timer()  # 初始化一个时间
    check_interval = 30  # 每30秒检查一次
    
    while True:
        await asyncio.sleep(check_interval)
        current_time = timer()
        last_message_time = tc.get(address, current_time)
        time_since_last_message = current_time - last_message_time
        
        # 记录心跳信息
        task_monitor_logger.debug(f"Message monitor for {address}: {time_since_last_message:.1f}s since last message")
        
        # 如果上次接受message的时间超过timeout，主动关闭连接并抛异常
        if time_since_last_message > timeout:
            task_monitor_logger.warning(f"No message received from {address} for {time_since_last_message:.1f} seconds, closing connection...")
            
            # 主动关闭连接
            if address in active_connections:
                websocket = active_connections[address]
                await safe_close_websocket(websocket, address)
            
            # 清理状态
            tc.pop(address, None)
            active_connections.pop(address, None)
            
            raise Exception(f"{address} after {timeout} seconds no message received")


async def connection_health_monitor():
    """
    连接健康监控 - 定期检查所有连接状态
    """
    while True:
        try:
            await asyncio.sleep(60)  # 每分钟检查一次
            
            current_time = timer()
            for address, websocket in list(active_connections.items()):
                try:
                    if is_websocket_closed(websocket):
                        task_monitor_logger.warning(f"Detected closed connection for {address}, cleaning up")
                        active_connections.pop(address, None)
                        tc.pop(address, None)
                    else:
                        # 检查连接是否响应
                        last_message_time = tc.get(address, current_time)
                        if current_time - last_message_time > 300:  # 5分钟没有消息
                            task_monitor_logger.warning(f"Connection to {address} appears stale, will be reset on next timeout")
                            
                except Exception as e:
                    task_monitor_logger.error(f"Error checking connection health for {address}: {e}")
                    # 如果检查出错，从活跃连接中移除
                    active_connections.pop(address, None)
                    tc.pop(address, None)
                    
        except Exception as e:
            task_monitor_logger.error(f"Error in connection health monitor: {e}")


async def graceful_shutdown():
    """
    优雅关闭所有连接
    """
    task_monitor_logger.info("Starting graceful shutdown...")
    
    # 关闭所有活跃连接
    for address, websocket in list(active_connections.items()):
        await safe_close_websocket(websocket, address)
    
    # 清理状态
    active_connections.clear()
    tc.clear()
    
    task_monitor_logger.info("Graceful shutdown completed")


async def main():
    global comfy_address_desc_map
    
    try:
        resource_info_list = await get_comfy_resources(use_cache=False)
        comfy_addr_list = list({i['address'] for i in resource_info_list})
        comfy_address_desc_map = await get_comfy_address_desc_map()
        
        if not comfy_addr_list:
            task_monitor_logger.error("No ComfyUI addresses found!")
            return
        
        task_monitor_logger.info(f"Starting monitoring for {len(comfy_addr_list)} addresses: {comfy_addr_list}")
        
        # 创建工作任务
        work_tasks = [asyncio.create_task(work(address)) for address in comfy_addr_list]
        
        # 启动健康监控
        health_task = asyncio.create_task(connection_health_monitor())
        
        # 等待所有任务
        await asyncio.gather(*work_tasks, health_task)
        
    except KeyboardInterrupt:
        task_monitor_logger.info("Received shutdown signal")
        await graceful_shutdown()
    except Exception as e:
        task_monitor_logger.error(f"Error in main: {e}")
        await graceful_shutdown()
        raise


if __name__ == "__main__":
    asyncio.run(main())
