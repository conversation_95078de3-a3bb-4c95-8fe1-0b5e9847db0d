import asyncio
import copy
import io
import json
import os
import sys
import datetime

import pymysql
from tortoise.functions import Count

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
# 获取当前脚本所在的目录
current_dir = os.path.dirname(current_file_path)
# 获取当前脚本所在目录的上一级目录
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_dir)

import requests
from conf.settings import *
from utils.utils import get_result_count_key
from databases.redis import cache_redis as cache
from tortoise import Tortoise

from databases.mysql import DB_ORM_CONFIG
from models.orm.base import User, Task, HeadshotStyle

task_id_start = TASK_ID_START  # 起始task_id, 数据量太大,增加查询效率 id有索引

yesterday = datetime.date.today() - datetime.timedelta(days=1)
yesterday_str = yesterday.strftime('%Y%m%d')

# sql中使用
yesterday_now = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday_start = yesterday_now.replace(hour=0, minute=0, second=0, microsecond=0)
yesterday_end = yesterday_now.replace(hour=23, minute=59, second=59, microsecond=999999)

type_desc_map_zh = {
    'passport': 'Passport  Maker',
    'headshot': 'Headshot Generator',
    'restore': 'Restoration',
    'rembg': 'Bg Remover',
    'bg_blur': 'Bg Blur',
    'face_swap': 'Face Swap',
    'text_remove': 'Watermark Remover',
    'object_remove': 'Object Remover',
    'object_swap': 'Replacer',
    'hair_swap': 'Hairstyle Changer',
    'ai_filter': 'Filter',
    'ai_baby': 'Baby',
    'enhance': 'Enhancer',
    'ai_bg': 'AI Bg',
    'ai_enlarge': 'Extender',
    'ai_skin_repair': 'Retouch',
    'ai_recolor': 'Recolor',
    'ai_face_cutout': 'Face Cutout',
    'art_v1': 'Image Generator ',
}


def send_dingtalk_alert(message, title="生图数据"):
    """
    发送钉钉告警消息
    :param webhook_url: 钉钉机器人Webhook URL
    :param title: 告警标题
    :param message: 发送的消息内容
    """
    url = "https://oapi.dingtalk.com/robot/send?access_token=9f0665e13862eaf2f2d58e044d30d6db85419097ca12b5374db4b5b5310de01c"

    headers = {
        'Content-Type': 'application/json'
    }
    message = f"[Aiease{yesterday_str}{title}]\n{message}"
    data = {
        "msgtype": "text",
        "text": {
            "content": message
        }
    }
    response = requests.post(url, headers=headers, json=data, verify=False)
    if response.status_code == 200:
        print("消息发送成功")
    else:
        print(f"消息发送失败，状态码: {response.status_code}")


def send_email(data_dict, **kwargs):
    to_users = ';'.join(USERS_RECEIVED_EMAIL)
    if not to_users:
        return
    # SendCloud API 配置
    api_user = SENDCLOUD_API_USER  # 替换为你的API用户
    api_key = SENDCLOUD_API_KEY  # 替换为你的API密钥

    # 邮件配置
    from_email = '<EMAIL>'  # 替换为你的发件人邮箱
    from_name = '<EMAIL>'  # 替换为你的发件人名称
    subject = 'Aiease数据统计'  # 邮件主题
    # 生成HTML表格
    html_content = f'<h2>aiease{yesterday_str}数据统计</h2>'
    html_content += '<table border="1" cellspacing="0" cellpadding="8">'
    html_content += '<tr><th>类型</th><th>数量</th><th>平均时间</th><th>使用人数</th></tr>'
    for key, value in data_dict.items():
        if not isinstance(value, list):
            value = [value]
        if len(value) == 3:
            html_content += f'<tr><td>{key}</td><td>{value[0]}</td><td>{value[1]}</td><td>{value[2]}</td></tr>'
        else:
            html_content += f'<tr><td>{key}</td><td>{value[0]}</td></tr>'

    html_content += '</table>'
    # 构建API请求数据
    data = {
        'apiUser': api_user,
        'apiKey': api_key,
        'from': from_email,
        'fromName': from_name,
        'to': to_users,
        'subject': subject,
        'html': html_content,
        'respEmailId': 'true'
    }
    files = {}
    country_csv_data = kwargs.get('country_csv_data', [])
    if country_csv_data:
        import csv
        fieldnames = ['任务类型', '发达国国家任务数', '发达国国家平均生图时间', '中等发达国国家任务数',
                      '中等发达国国家平均生图时间', '非发达国国家任务数', '非发达国国家平均生图时间']
        country_csv_data.insert(0, fieldnames)
        csv_output = io.StringIO()
        csv_writer = csv.writer(csv_output)

        csv_writer.writerows(country_csv_data)

        # 获取内存中的 CSV 文件内容
        csv_content = csv_output.getvalue()
        csv_output.close()
        files = {
            'attachments': (f'{yesterday_str}_country_info.csv', csv_content, 'text/csv')
        }
    # 发送请求
    response = requests.post(SENDCLOUD_API_URL, data=data, verify=False, files=files)

    # 打印响应结果
    if response.status_code == 200:
        print('邮件发送成功:', response.json())
    else:
        print('邮件发送失败:', response.json())


def get_result_count_data():
    """
    获取成功出图任务数
    """
    key = get_result_count_key(date_str=yesterday_str)
    return cache.get(key) or 0


def get_max_queue_data():
    """
    获取最大队列任务数
    """
    key = f"comfy:max:queue:{yesterday_str}"
    return cache.get(key) or 0


async def get_user_data():
    """
    获取mysql中的user数据
    获取所需的用户注册数据
    :return:
    """

    # 昨日注册数
    yesterday_register = await User.filter(bio__not="temp", created_at__range=(yesterday_start, yesterday_end)).count()
    yesterday_visit_user = await User.filter(bio="temp", created_at__range=(yesterday_start, yesterday_end)).count()
    all_register = await User.filter(bio__not="temp").count()  # 所有注册数
    tasks = await Task.filter(id__gte=task_id_start, task_state=2, created_at__range=(yesterday_start, yesterday_end),
                              end_at__isnull=False).values('userid')
    userid_set = set()
    for task in tasks:
        userid_set.add(task['userid'])
    data = {
        'yesterday_register': yesterday_register,
        'yesterday_visit_user': yesterday_visit_user,
        'all_register': all_register,
        'yesterday_daily_activity_results': userid_set
    }
    return data


async def get_single_type_task_data(tasktype):
    """
    统计某种类型的数据
    :return:
    """
    success_tasks = await Task.filter(id__gte=task_id_start, task_state=2, tasktype=tasktype,
                                      created_at__range=(yesterday_start, yesterday_end),
                                      end_at__isnull=False).values('created_at', 'end_at')
    # 成功任务数
    success_count = await Task.filter(id__gte=task_id_start, task_state=2, tasktype=tasktype,
                                      created_at__range=(yesterday_start, yesterday_end),
                                      end_at__isnull=False).count()
    # 失败任务书
    failed_count = await Task.filter(id__gte=task_id_start, task_state=4, tasktype=tasktype,
                                     created_at__range=(yesterday_start, yesterday_end)).count()

    # 已知错误 没人脸/没mask
    known_errors_count = await Task.filter(id__gte=task_id_start, task_state=5, tasktype=tasktype,
                                           created_at__range=(yesterday_start, yesterday_end)).count()

    # 挂起的任务
    pending_count = await Task.filter(id__gte=task_id_start, task_state=1, tasktype=tasktype,
                                      created_at__range=(yesterday_start, yesterday_end)).count()

    results = await Task.filter(id__gte=task_id_start, task_state=2, tasktype=tasktype,
                                created_at__range=(yesterday_start, yesterday_end),
                                end_at__isnull=False).group_by(
        "userid").annotate(
        count=Count("id")).values("userid", "count")
    count_list = []
    for r in results:
        count_list.append(r['count'])

    top_10_list = sorted(count_list, reverse=True)[:10]  # Top10用户任务数
    user_num = len(count_list)  # 使用用户数
    avg_image_num = round(success_count / user_num, 2)  # Headshot平均生图数
    total_time = 0
    for task in success_tasks:
        created_at = task.get('created_at')
        end_at = task.get('end_at')
        if not created_at and not end_at:
            continue
        try:
            use_time = (end_at - created_at).total_seconds()
            # 兼容数据库默认是utc时间
            if use_time > 8 * 60 * 60:
                use_time = use_time - 8 * 60 * 60
            total_time += use_time
        except:
            success_count -= 1
            continue
    avg_time = round(total_time / success_count, 2)  # 平均生图时间
    info = {
        'avg_time': avg_time,
        'user_num': user_num,
        'avg_image_num': avg_image_num,
        'top_10_list': top_10_list,
        'success_count': success_count,
        'failed_count': failed_count,
        'known_errors_count': known_errors_count,
        'pending_count': pending_count
    }
    return info


async def get_task_data():
    """
    获取mysql中的task数据
    获取所需的任务数据
    :return:
    """
    # 昨日任务数
    yesterday_success_task_count = await Task.filter(id__gte=task_id_start, task_state=2,
                                                     created_at__range=(yesterday_start, yesterday_end)).count()
    yesterday_failed_task_count = await Task.filter(id__gte=task_id_start, task_state=4,
                                                    created_at__range=(yesterday_start, yesterday_end)).count()
    yesterday_known_error_task_count = await Task.filter(id__gte=task_id_start, task_state=5,
                                                         created_at__range=(yesterday_start, yesterday_end)).count()
    yesterday_pending_task_count = await Task.filter(id__gte=task_id_start, task_state=1,
                                                     created_at__range=(yesterday_start, yesterday_end)).count()
    results = await Task.filter(id__gte=task_id_start, task_state=2,
                                created_at__range=(yesterday_start, yesterday_end)).group_by(
        "tasktype").annotate(
        count=Count("id")).values("tasktype", "count")
    yesterday_task_info_dict = {}
    for result in results:
        tasktype = result['tasktype']
        info = await get_single_type_task_data(tasktype=tasktype)
        avg_time = info['avg_time']
        user_num = info['user_num']
        count = result['count']
        tasktype_cn = type_desc_map_zh.get(tasktype)
        if not tasktype_cn:
            continue
        yesterday_task_info_dict.update({tasktype_cn: [count, f'{avg_time}s', user_num]})
    data = {
        'yesterday_task_info_dict': yesterday_task_info_dict,
        'yesterday_success_task_count': yesterday_success_task_count,
        'yesterday_failed_task_count': yesterday_failed_task_count,
        'yesterday_known_error_task_count': yesterday_known_error_task_count,
        'yesterday_pending_task_count': yesterday_pending_task_count
    }
    return data


async def send_headshot_task_style_info():
    """
    统计每种类型的数据
    :return:
    """
    tasks = await Task.filter(id__gte=task_id_start, task_state=2, tasktype='headshot',
                              created_at__range=(yesterday_start, yesterday_end),
                              end_at__isnull=False).values(
        'task_params', 'id')
    styles = await HeadshotStyle.filter(status=1).values('id', 'sort', 'style_name', 'gender', 'style')
    style_dict = {}
    for style in styles:
        style_id = style['id']
        style_name = style['style_name']
        style_num = style['sort']
        style_class = style['style']
        style_gender = style['gender']
        style_dict[style_id] = f"{style_num}-{style_name}-{style_class}-{style_gender}"
    res = {}
    for task in tasks:
        task_params = json.loads(task['task_params']) if task['task_params'] else {}
        if not task_params:
            continue
        try:
            extra_data = task_params['extra_data']
            style_id = extra_data['style_id']
            style_desc = style_dict[style_id]
            res.setdefault(style_desc, 0)
            res[style_desc] += 1
        except:
            pass
    sorted_res = dict(sorted(res.items(), key=lambda x: x[1], reverse=True))
    send_dingtalk_alert(message=json.dumps(sorted_res, ensure_ascii=False, indent=4), title="headshot风格数据")


def get_country_task_info():
    """
    发送一些国家的生图数据
    :return:
    """
    from dbutils.pooled_db import PooledDB
    from pymysql.cursors import DictCursor
    # 数据库配置
    db_config = copy.deepcopy(MYSQL_CONFIG)
    db_config['cursorclass'] = DictCursor
    db_config['init_command'] = "SET time_zone = '+08:00'"

    # 创建数据库连接池
    pool = PooledDB(
        creator=pymysql,  # 使用的数据库连接模块
        maxconnections=10,  # 连接池允许的最大连接数
        mincached=2,  # 初始化时创建的连接数
        maxcached=5,  # 连接池中最多闲置的连接数
        blocking=True,  # 连接池中无可用连接后是否阻塞等待
        **db_config
    )
    connection = pool.connection()
    try:
        with connection.cursor() as cursor:
            # 编写SQL查询语句
            sql = f"""SELECT
    ci.priority,
    t.tasktype as task_type,
    COUNT(*) AS task_number,
    CAST(AVG(TIMESTAMPDIFF(SECOND, t.created_at, t.end_at)) AS CHAR) AS task_avg_time
FROM
    task t
LEFT JOIN
    country_info ci
ON
    t.country = ci.iso_code
WHERE
    t.id > {task_id_start}
    AND t.end_at IS NOT NULL
    AND t.task_state = 2
    AND t.created_at BETWEEN "{yesterday_start}" AND "{yesterday_end}"
    AND ci.priority IN (1, 2, 3)
GROUP BY
    ci.priority,
    t.tasktype
ORDER BY
    ci.priority,
    t.tasktype;
            """
            # 执行查询
            cursor.execute(sql)
            # 获取查询结果
            results = cursor.fetchall()
            print(results)
            res = {}
            csv_data = []
            for i in results:
                priority = i['priority']
                task_number = i['task_number']
                task_avg_time = i['task_avg_time']
                task_type = type_desc_map_zh.get(i['task_type'], i['task_type'])
                res.setdefault(task_type, {})
                res[task_type][priority] = [task_number, task_avg_time]
            for t, info in res.items():
                p = [t]
                p1 = info.get(1, ["", ""])
                p2 = info.get(2, ["", ""])
                p3 = info.get(3, ["", ""])
                p.extend(p3)
                p.extend(p2)
                p.extend(p1)
                csv_data.append(p)

            return csv_data
    finally:
        connection.close()
        pool.close()


async def main():
    await Tortoise.init(config=DB_ORM_CONFIG)
    try:
        user_data = await get_user_data()
        task_data = await get_task_data()

        data = copy.deepcopy(task_data['yesterday_task_info_dict'])
        data.update({
            '今日注册用户': user_data['yesterday_register'],
            '今日访客用户': user_data['yesterday_visit_user'],
            '所有注册用户': user_data['all_register'],
            '成功等待出图任务数': get_result_count_data(),  # 成功等待出图任务数
            '最大任务队列数': get_max_queue_data(),  # 最大队列任务数
            '成功任务数': task_data['yesterday_success_task_count'],
            '失败任务数': task_data['yesterday_failed_task_count'] + task_data['yesterday_pending_task_count'],
            '用户原因失败任务数': task_data['yesterday_known_error_task_count'],
            '今日用户日活数': len(user_data['yesterday_daily_activity_results'])
        })
        try:
            country_csv_data = get_country_task_info()
        except:
            country_csv_data = []
        send_email(data, country_csv_data=country_csv_data)
    except Exception as e:
        print(f"Main execution error: {e}")
    finally:
        print('data state end')

    # aiease数据 发送到钉钉webhook的
    data = {}
    for tasktype, desc in type_desc_map_zh.items():
        try:
            info = await get_single_type_task_data(tasktype=tasktype)
            avg_time = info.get('avg_time')
            user_num = info.get('user_num')
            avg_image_num = info.get('avg_image_num')
            failed_count = info.get('failed_count', 0)
            top_10_list = info.get('top_10_list')
            known_errors_count = info.get('known_errors_count', 0)
            pending_count = info.get('pending_count', 0)
            data.update({
                desc: {
                    '平均生图时间': avg_time,
                    '功能使用用户量': user_num,
                    '平均生图任务数': avg_image_num,
                    '失败任务数': failed_count + pending_count,
                    '用户原因失败任务数': known_errors_count,
                    'top10生图数量': top_10_list
                }
            })
        except Exception as e:
            print(f"{tasktype} M execution error: {e}")
    send_dingtalk_alert(message=json.dumps(data, ensure_ascii=False, indent=4))


if __name__ == '__main__':
    asyncio.run(main())
