import os
import yaml

from conf.config import settings

DEFAULT_REDIS = {}  # 默认缓存redis配置

TASK_REDIS = {}  # 任务redis配置

MQ_REDIS = {}  # 消息队列redis配置

WARN_REDIS = {}  # 告警redis配置

DEFAULT_CLIENT_ID = "aiease"

MONGO_URI, DB_NAME = "", ""

aiease_allowed_domains = []

global_acceleration_cos_url = ""

auth_secret_map = {}

SENDCLOUD_API_USER = ''

SENDCLOUD_API_KEY = ''

SENDCLOUD_API_URL = ''

DEBUG = True

ssh_key_file_path = ""

special_ssh_addr_port_map = {}

tp_task_status = 'aiease'

yaml_setting_path = os.path.join(settings.CONF_DIR, 'settings.yaml')

if not os.path.exists(yaml_setting_path):
    raise FileNotFoundError("settings.yaml not exists")

with open(yaml_setting_path, 'r', encoding='utf-8') as f:
    cfg = f.read()
    ys = yaml.load(cfg, Loader=yaml.Loader)

locals().update(ys)

WARN_BROKER_URL = 'redis://:{}@{}:{}/{}'.format(
    WARN_REDIS.get('password', ''),
    WARN_REDIS.get('host'),
    WARN_REDIS.get('port'),
    WARN_REDIS.get('db', 1)
)

secret_auth_map = {v: k for k, v in auth_secret_map.items()}
