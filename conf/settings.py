import os
import yaml

from conf.config import settings

DEFAULT_REDIS = {}  # 默认缓存redis配置

TASK_REDIS = {}  # 任务redis配置

WARN_REDIS = {}  # 告警redis配置

USER_REDIS = {}  # 用户redis配置

NODES_DOWNLOAD_IMAGE = False  # 是否开启节点下载图片

ssh_key_file_path = ""  # comfy服务器ssh连接的key

special_ssh_addr_port_map = {}  # ssh 特殊端口配置

MAX_CONCURRENCY = 500  # celery最大并发数

SECRET_KEY = "xxxxaiease!@#"

UPLOAD_MIN_LIMIT = 1  # 1B

UPLOAD_MAX_LIMIT = 2 * 1024 * 1024  # 2M

IMAGE_SERVICE = "http://192.168.1.0:9000"

MYSQL_CONNECTION = "mysql://root:pwd@127.0.0.1:3306/aiease"

MYSQL_CONFIG = {
}
COUNTRY_AD_MAP = {}  # 国家ISO CODE对应广告地址

ai_temp_bucket_name = 'ai-storage-temp'

GOOGLE_AUTH_CLIENTID = "xxxx.apps.googleusercontent.com"

DEFAULT_CLIENT_ID = "aiease"

TASK_ID_START = 0

# 腾讯云对象存储配置 start

default_cos_url = "pub-static.aiease.ai"  # 默认地址 用于图片上传(上传时使用内网)

global_acceleration_cos_url = "static.aiease.ai"  # 全球加速  用于用户访问

aiease_allowed_domains = []

key_str = ""  # 定义与前端加密解密通信的密钥

geo_lite_path = os.path.join(settings.CONF_DIR, 'GeoLite2-City.mmdb')  # GeoLite2-City.mmdb的路径

# 配置访问密钥和密钥 ID
access_key_id = ''

secret_access_key = ''

region = ''  # 根据实际情况修改区域

upload_bucket = ''

output_bucket = ''

# 腾讯云对象存储配置 end

SENDCLOUD_API_USER = ''
SENDCLOUD_API_KEY = ''
SENDCLOUD_API_URL = ''

USERS_RECEIVED_EMAIL = []

DEBUG = True

user_gen_img_dispatch_count = 50
yaml_setting_path = os.path.join(settings.CONF_DIR, 'settings.yaml')

if not os.path.exists(yaml_setting_path):
    raise FileNotFoundError("settings.yaml not exists")

with open(yaml_setting_path, 'r', encoding='utf-8') as f:
    cfg = f.read()
    ys = yaml.load(cfg, Loader=yaml.Loader)

locals().update(ys)

WARN_BROKER_URL = 'redis://:{}@{}:{}/{}'.format(
    WARN_REDIS.get('password', ''),
    WARN_REDIS.get('host'),
    WARN_REDIS.get('port'),
    WARN_REDIS.get('db', 1)
)

if not os.path.exists(geo_lite_path):
    raise FileNotFoundError(f"{geo_lite_path} not exists")
