MONGO_URI: 'mongodb://xxx:xxx@*************:27017'
DB_NAME: 'xxx'

#redis配置
DEFAULT_REDIS:
  host: '************'
  port: 16379
  password: 'xxxx'
  db: 13


TASK_REDIS:
  host: '************'
  port: 16379
  password: 'xxxxx'
  db: 14

WARN_REDIS:
  host: '************'
  port: 16379
  password: 'xxxx'
  db: 15


DEFAULT_CLIENT_ID: "aiease-pix-dev-xr-localhost"

DEBUG: True

# 腾讯云对象存储配置
global_acceleration_cos_url: "static.aiease.ai"  # 全球加速  用于用户访问


aiease_allowed_domains: [
  'static.aiease.ai',  # oss全球加速加速地址
  'pub-static.aiease.ai',  # 默认地址  内网传输的话是内网地址 正常用户使用是oss默认地址
  'aieaase-1324066212.cos.accelerate.myqcloud.com',  # oss全球加速加速地址
  'aieaase-1324066212.cos.na-siliconvalley.myqcloud.com',  # 默认地址  内网传输的话是内网地址 正常用户使用是oss默认地址
]

