# WebSocket连接管理改进方案

## 🎯 **您提出的问题非常重要！**

您说得完全正确 - 在重连之前应该手动关闭上一个连接。这是一个关键的资源管理问题，我已经对代码进行了全面改进。

## 🔍 **原代码的问题**

### 1. **资源泄漏风险**
```python
# 原代码问题
async with websockets.connect(ws_url, ...) as websocket:
    # 异常时可能不会立即释放连接
    await asyncio.gather(...)
```

### 2. **缺少连接状态管理**
- 没有跟踪活跃连接
- 无法主动关闭有问题的连接
- 重连时可能产生重复连接

### 3. **异常处理不完善**
- 连接异常时没有清理资源
- 缺少优雅关闭机制

## 🛠️ **改进方案**

### 1. **添加连接状态管理**
```python
# 新增全局状态管理
active_connections = {}  # 存储活跃的WebSocket连接
connection_tasks = {}    # 存储连接任务
```

### 2. **实现主动连接关闭**
```python
async def close_existing_connection(address):
    """关闭指定地址的现有连接"""
    if address in active_connections:
        try:
            websocket = active_connections[address]
            if not websocket.closed:
                task_monitor_logger.info(f"Closing existing connection to {address}")
                await websocket.close()
                await asyncio.sleep(0.5)  # 等待连接完全关闭
        except Exception as e:
            task_monitor_logger.warning(f"Error closing existing connection: {e}")
        finally:
            active_connections.pop(address, None)
```

### 3. **改进的工作流程**
```python
async def work(address):
    while True:
        try:
            # 🔥 关键改进：重连前先关闭现有连接
            await close_existing_connection(address)
            
            # 建立新连接
            websocket = await websockets.connect(ws_url, ...)
            active_connections[address] = websocket  # 跟踪连接
            
            # 启动监听任务
            await asyncio.gather(...)
            
        except Exception as e:
            # 异常时清理资源
            await cleanup_connection(address, websocket)
```

### 4. **消息监控主动关闭**
```python
async def message_monitor(address, timeout=180):
    # 超时时主动关闭连接
    if time_since_last_message > timeout:
        if address in active_connections:
            websocket = active_connections[address]
            if not websocket.closed:
                await websocket.close()  # 🔥 主动关闭
        raise Exception(f"Timeout, connection closed")
```

## 📁 **提供的改进版本**

### 1. **改进的原文件** (`scripts/task_monitor.py`)
- 添加了连接状态管理
- 实现了主动连接关闭
- 改进了异常处理和资源清理

### 2. **完全重构版本** (`scripts/task_monitor_improved.py`)
- 使用连接管理器类
- 完善的生命周期管理
- 优雅关闭机制
- 信号处理支持

## 🔧 **关键改进点**

### 1. **重连前主动关闭**
```python
# 每次重连前都会：
await close_existing_connection(address)  # 关闭旧连接
websocket = await websockets.connect(...)  # 建立新连接
```

### 2. **连接状态跟踪**
```python
active_connections[address] = websocket  # 跟踪活跃连接
connection_tasks[address] = task         # 跟踪连接任务
```

### 3. **资源清理保证**
```python
async def cleanup_connection(address, websocket):
    try:
        if websocket and not websocket.closed:
            await websocket.close()
    finally:
        active_connections.pop(address, None)
        tc.pop(address, None)
```

### 4. **健康监控**
```python
async def connection_health_monitor():
    # 定期检查连接状态
    # 清理已关闭的连接
    # 检测僵尸连接
```

## 🚀 **使用建议**

### 1. **立即部署改进版本**
```bash
# 备份原文件
cp scripts/task_monitor.py scripts/task_monitor.py.backup

# 重启服务应用改进
sudo supervisorctl restart aiease_monitor
```

### 2. **测试完全重构版本**
```bash
# 测试新版本
python scripts/task_monitor_improved.py
```

### 3. **监控连接状态**
```bash
# 查看日志确认连接管理工作正常
tail -f logs/task_monitor.log | grep -E "(Closing|Successfully connected|Disconnecting)"
```

## 📊 **预期效果**

### 1. **解决资源泄漏**
- ✅ 重连前主动关闭旧连接
- ✅ 异常时保证资源清理
- ✅ 避免连接堆积

### 2. **提高连接稳定性**
- ✅ 连接状态实时跟踪
- ✅ 主动检测和处理问题连接
- ✅ 优雅的关闭和重连机制

### 3. **更好的可观测性**
- ✅ 详细的连接生命周期日志
- ✅ 连接健康状态监控
- ✅ 异常情况的完整记录

## 🔍 **验证方法**

### 1. **检查连接数量**
```bash
# 检查系统WebSocket连接数
netstat -an | grep :8188 | grep ESTABLISHED | wc -l
```

### 2. **监控日志输出**
```bash
# 查看连接管理日志
tail -f logs/task_monitor.log | grep -E "(Closing existing|Successfully connected)"
```

### 3. **测试重连机制**
```bash
# 模拟网络中断测试重连
# 观察是否正确关闭旧连接并建立新连接
```

## 💡 **总结**

您的观察非常准确！原代码确实存在连接管理问题。现在的改进版本：

1. **重连前主动关闭旧连接** - 解决资源泄漏
2. **完善的连接状态管理** - 实时跟踪连接状态  
3. **异常时保证资源清理** - 避免僵尸连接
4. **优雅的关闭机制** - 支持平滑重启

这些改进将显著提高WebSocket连接的稳定性和资源利用效率。
