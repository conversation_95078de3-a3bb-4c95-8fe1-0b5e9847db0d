import asyncio
import json

from slowapi import Limiter
from starlette.requests import Request
from starlette.responses import JSONResponse
from fastapi import APIRouter, Depends

from celery_manage.tasks import gen_task
from core.Auth import get_authorization
from core.Response import resp_200, resp_450,resp_4210
from databases.redis import async_cache_redis,cache_redis
from dependencies.common import get_platform, get_priority, get_country_iso_code
from models.schemas.common import Img2imgItem, Text2imgItem
from services.styles import get_headshot_style, get_aifilter_style, get_aihair_style
from services.task import create_task, get_text2image_list
from utils.caches import increment_task, decrement_task
from utils.defines import country_priority_number_map, country_priority_limit_map
from utils.fmts import *
from utils.lock_utils import acquire_lock
from utils.utils import get_ip_and_token_limit, get_resource
from conf.settings import user_gen_img_dispatch_count
from utils.time_utils import get_utc_format_day
limiter = Limiter(key_func=get_ip_and_token_limit)

router = APIRouter()

priority_dict = {}
priority_lock = asyncio.Lock()


def get_task_number(priority: int) -> int | None:
    """
    :param priority:
    :return:
    """
    return country_priority_number_map.get(priority)


async def check_data(auth, post_data):
    """
    检查参数
    :param auth:
    :param post_data:
    :return:
    """
    userinfo = auth[0]
    email = userinfo['email']
    extra_data = post_data.extra_data
    gen_type = post_data.gen_type
    # 分布式锁保证同一用户同一类型任务只能有一个在执行中
    key = f"{email}-{gen_type}"
    if not acquire_lock(key, lock_timeout=2):
        return False, "Task in progress. Please wait until it finishes"
    # 查看当前是否由当前类型任务在执行中
    key = PROCESSING_EMAIL_TO_PROMPT_FMT.format(gen_type, email)
    task_in_process = await async_cache_redis.get(key)  # 是否有任务执行中
    if task_in_process:
        return False, "Task in progress. Please wait until it finishes"

    if gen_type == 'headshot':
        style_info = await get_headshot_style(id=extra_data.get('style_id'), status=1)
        if not style_info:
            return False, "gen task failed, not support style id"

    elif gen_type == 'ai_filter':
        style_info = await get_aifilter_style(id=extra_data.get('style_id'), status=1)
        if not style_info:
            return False, "gen task failed, not support style id"
    elif gen_type == 'hair_swap':
        style_id = extra_data.get('style_id')
        if style_id:
            style_info = await get_aihair_style(id=style_id, status=1)
            if not style_info:
                return False, "gen task failed, not support style id"
    return True, ""


@router.post(
    "/img2img",
    summary="图生图接口",
    description="图生图接口",
    name="图生图接口"
)
@limiter.limit("20/minute")
async def img2img(
        request: Request,
        post_data: Img2imgItem,
        auth: dict = Depends(get_authorization),
        platform: str = Depends(get_platform),
        priority: int = Depends(get_priority),  # 优先级别
        iso_code: str = Depends(get_country_iso_code)  # 国家
) -> JSONResponse:
    userinfo = auth[0]
    extra_data = post_data.extra_data
    gen_type = post_data.gen_type
    tag = gen_type
    ret, message = await check_data(auth, post_data)
    if not ret:
        return resp_450(message=message)
    if priority == 999:
        # 支持禁用某些国家ip生图 只需要数据库配置优先级是999
        return resp_450(message="Forbidden, Please try again later or contact support.")
    max_limit = country_priority_limit_map.get(priority, 15)  # 根据优先级获取在comfyui中的最大限制
    comfy_address, msg = await get_resource(max_limit=max_limit, tag=tag)  # 获取comfy的资源
    if not comfy_address:
        return resp_450(message=msg)

    increment_task(server=comfy_address)
    number = country_priority_number_map.get(priority)  # 根据优先级获取在comfyui中的number
    if number is not None:
        extra_data['number'] = number
    insert_info = {
        'tasktype': gen_type,
        'prompt_id': "",
        'task_state': 0,
        'userid': userinfo['id'],
        "source_url": extra_data.get("img_url", ""),
        "image_urls": "",
        "thumbnail_urls": "",
        "platform": platform,
        "country": iso_code,
        "task_params": json.dumps(post_data.dict())
    }
    res = await create_task(**insert_info)
    if not res:
        decrement_task(server=comfy_address)
        return resp_450(message="Create task failed, Please try again")
    task_id = int(res.pk)
    gen_task.delay(task_id, gen_type, userinfo, extra_data, comfy_address)

    return resp_200(data={"task_id": task_id})


@router.post(
    "/text2img",
    summary="文生图接口",
    description="文生图接口",
    name="文生图接口"
)
@limiter.limit("20/minute")
async def text2img(
        request: Request,
        post_data: Text2imgItem,
        auth: dict = Depends(get_authorization),
        platform: str = Depends(get_platform),
        priority: int = Depends(get_priority),  # 优先级别
        iso_code: str = Depends(get_country_iso_code)  # 国家
) -> JSONResponse:
    userinfo = auth[0]
    extra_data = post_data.extra_data
    gen_type = post_data.gen_type
    tag = gen_type
    ret, message = await check_data(auth, post_data)
    if not ret:
        return resp_450(message=message)
    if priority == 999:
        # 支持禁用某些国家ip生图 只需要数据库配置优先级是999
        return resp_450(message="Forbidden, Please try again later or contact support.")
    max_limit = country_priority_limit_map.get(priority, 15)  # 根据优先级获取在comfyui中的最大限制
    comfy_address, msg = await get_resource(max_limit=max_limit, tag=tag)  # 获取comfy的资源
    if not comfy_address:
        return resp_450(message=msg)
    user_gen_count_key = USER_EMAIL_GEN_IMG_COUNT_FMT.format(userinfo['email'], get_utc_format_day())
    user_gen_count = cache_redis.get(user_gen_count_key)
    if user_gen_count:
        user_gen_count = int(user_gen_count)
        if user_gen_count >= user_gen_img_dispatch_count:
            return resp_4210(data={"count": user_gen_count})
    else:
        cache_redis.incrby(user_gen_count_key, 0)
        cache_redis.expire(user_gen_count_key, 24 * 60 * 60)
        user_gen_count = 1
    increment_task(server=comfy_address)
    number = country_priority_number_map.get(priority)  # 根据优先级获取在comfyui中的number
    if number is not None:
        extra_data['number'] = number
    insert_info = {
        'tasktype': gen_type,
        'prompt_id': "",
        'task_state': 0,
        'userid': userinfo['id'],
        "source_url": "",
        "image_urls": "",
        "thumbnail_urls": "",
        "platform": platform,
        "country": iso_code,
        "task_params": json.dumps(post_data.dict())
    }
    res = await create_task(**insert_info)
    if not res:
        decrement_task(server=comfy_address)
        return resp_450(message="Create task failed, Please try again")
    task_id = int(res.pk)
    gen_task.delay(task_id, gen_type, userinfo, extra_data, comfy_address, 'text2img')
    cache_redis.incrby(user_gen_count_key, 1)
    return resp_200(data={"task_id": task_id, "count": user_gen_count})

@router.get(
    "/genImgCount",
    summary="文生图接口",
    description="文生图接口",
    name="文生图接口"
)
@limiter.limit("20/minute")
async def gen_img_count(
        request: Request,
        auth: dict = Depends(get_authorization)
) -> JSONResponse:
    userinfo = auth[0]
    user_gen_count_key = USER_EMAIL_GEN_IMG_COUNT_FMT.format(userinfo['email'], get_utc_format_day())
    user_gen_count = cache_redis.get(user_gen_count_key)
    if not user_gen_count:
        user_gen_count = 0
    return resp_200(data={"count": int(user_gen_count)})

@router.get(
    "/list",
    summary="文生图历史接口",
    description="文生图历史接口",
    name="文生图历史接口"
)
@limiter.limit("20/minute")
async def list(
        request: Request,
        auth: dict = Depends(get_authorization),
        cursor: int = 0,
        limit: int = 20,
        task_type: str | None = None,
) -> JSONResponse:
    userinfo = auth[0]
    userid = userinfo['id']
    data = await get_text2image_list(
        cursor=cursor, limit=limit, tasktype=task_type,
        userid=userid,
        task_state=2,
        img_status=1,
        reverse=True
    )
    return resp_200(data=data)
