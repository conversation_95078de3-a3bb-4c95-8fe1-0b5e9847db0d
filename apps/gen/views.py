from starlette.requests import Request
from starlette.responses import JSONResponse
from fastapi import APIRouter, Depends
from conf.settings import DEFAULT_CLIENT_ID

from core.Auth import get_authorization
from core.Response import resp_200, resp_450
from schemas.gen import *
from celery_manage.tasks import gen_task

from services.task import create_task, get_task_info

from utils.time_utils import current_timer
from utils.utils import get_resource
from services.server_source import get_comfy_resources
from utils.caches import get_all_comfy_queue_num

router = APIRouter()


@router.get(
    "/result/{task_id}",
    description="result",
)
async def result(
        request: Request,
        task_id: str,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    info = await get_task_info(task_id)
    return resp_200(data=info)


@router.get(
    "/comfy_status/{task_type}",
    description="comfy-status",
)
async def comfy_status(
        request: Request,
        task_type: str,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    all_comfy = await get_comfy_resources(use_cache=True)
    all_num = await get_all_comfy_queue_num()
    available_count = 0
    if all_comfy:
        for item in all_comfy:
            if item["status"] and (task_type in item.get('tag',[])  or item['tag'] == []):
                if all_num:
                    num = all_num[item['address']]
                    if num < 3:
                        available_count += 1
                else:
                    available_count += 1
    return resp_200(data=available_count)

async def gen_prompt(*, auth=None, data: Img2ImgBaseItem):
    """
    :param data:
    :param auth:
    :return:
    """
    if auth is None:
        auth = {}
    params = dict(data)  # comfy需要的参数
    extra = data.extra  # 额外参数
    params.update(extra)
    origin_params = dict(data)  # 原始参数
    task_type = origin_params.get("task_type")
    params['func_type'] = task_type
    comfy_server, msg = await get_resource(tag=task_type, priority=params.get("priority"))  # 获取comfy的资源
    if not comfy_server:
        return resp_450(message=msg)
    insert_info = {
        'task_type': task_type,
        'params': params,
        'origin_params': origin_params,
        'status': 0,  # 未开始
        'create_time': current_timer(),
        'update_time': current_timer(),
        'comfy_server': comfy_server,
        'auth': auth,
        'client_id': DEFAULT_CLIENT_ID
    }
    task = await create_task(insert_info)
    task_id = str(task.inserted_id)
    gen_task.delay(task_id, params, comfy_server)
    return resp_200(data={"task_id": task_id})


@router.post(
    "/headshot",
    description="headshot",
)
async def headshot(
        request: Request,
        data: HeadshotItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/rembg",
    description="rembg",
)
async def rembg(
        request: Request,
        data: RembgItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/bg_blur",
    description="bg_blur",
)
async def bg_blur(
        request: Request,
        data: BgBlurItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/passport",
    description="passport",
)
async def passport(
        request: Request,
        data: PassportItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/enhance",
    description="enhance",
)
async def enhance(
        request: Request,
        data: EnhanceItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/ai_filter",
    description="ai_filter",
)
async def ai_filter(
        request: Request,
        data: AiFilterItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/restore",
    description="restore",
)
async def restore(
        request: Request,
        data: RestoreItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/face_crop",
    description="face_crop",
)
async def face_crop(
        request: Request,
        data: FaceCropItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/face_swap",
    description="face_swap",
)
async def face_swap(
        request: Request,
        data: FaceSwapItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/object_remove",
    description="object_remove",
)
async def object_remove(
        request: Request,
        data: ObjectRemoveItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/text_remove",
    description="text_remove",
)
async def text_remove(
        request: Request,
        data: TextRemoveItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/ai_baby",
    description="ai_baby",
)
async def ai_baby(
        request: Request,
        data: AiBabyItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/object_swap",
    description="object_swap",
)
async def object_swap(
        request: Request,
        data: ObjectSwapItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/hair_swap",
    description="hair_swap",
)
async def hair_swap(
        request: Request,
        data: HairSwapItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/ai_bg",
    description="ai_bg",
)
async def ai_bg(
        request: Request,
        data: AiBgItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/ai_enlarge",
    description="ai_enlarge",
)
async def ai_enlarge(
        request: Request,
        data: AiEnlargeItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/ai_skin_repair",
    description="ai_skin_repair",
)
async def ai_skin_repair(
        request: Request,
        data: AiSkinRepairItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/ai_recolor",
    description="ai_recolor",
)
async def ai_recolor(
        request: Request,
        data: AiRecolorItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/art_v1",
    description="art_v1",
)
async def art_v1(
        request: Request,
        data: AiArtV1Item,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp


@router.post(
    "/ai_face_cutout",
    description="ai_face_cutout",
)
async def ai_face_cutout(
        request: Request,
        data: AiFaceCutoutItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp

@router.post(
    "/try_on",
    description="try_on",
)
async def try_on(
        request: Request,
        data: TryOnItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp

@router.post(
    "/ai_wardrobe",
    description="ai_wardrobe",
)
async def ai_wardrobe(
        request: Request,
        data: AiWardrobeItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp

@router.post(
    "/ai_filter_ghibli",
    description="ai_filter_ghibli",
)
async def ai_filter_ghibli(
        request: Request,
        data: AiFilterGhibliItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp
@router.post(
    "/headshot_pro",
    description="headshot_pro",
)
async def headshot_pro(
        request: Request,
        data: HeadshotProItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp

@router.post(
    "/face_detect",
    description="face_detect",
)
async def face_detect(
        request: Request,
        data: FaceDetectItem,
        auth: dict = Depends(get_authorization),
) -> JSONResponse:
    resp = await gen_prompt(data=data, auth=auth)
    return resp