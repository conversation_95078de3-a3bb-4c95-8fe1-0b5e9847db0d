SAMPLER_LIST = [
    "euler", "euler_ancestral", "heun", "heunpp2", "dpm_2", "dpm_2_ancestral", "lms", "dpm_fast", "dpm_adaptive",
    "dpmpp_2s_ancestral", "dpmpp_sde", "dpmpp_sde_gpu", "dpmpp_2m", "dpmpp_2m_sde", "dpmpp_2m_sde_gpu",
    "dpmpp_3m_sde", "dpmpp_3m_sde_gpu", "ddpm", "lcm"
]

SCHEDULER_LIST = ["normal", "karras", "exponential", "sgm_uniform", "simple", "ddim_uniform"]

enhance_conf = {
    'general_2x': {
        'model': "RealESRGAN_x2plus.pth",
        'face_restore': False,
        'recolor': False,
    },
    'general_4x': {
        'model': "RealESRGAN_x4plus.pth",
        'face_restore': False,
        'recolor': False,
        'support_restore': True
    },
    'anime_2x': {
        'model': "RealESRGANv2-animevideo-xsx2.pth",
        'face_restore': False,
        'recolor': False,
    },
    'anime_4x': {
        'model': "RealESRGAN_x4plus_anime_6B.pth",
        'restore': False,
        'recolor': False,
    },
    'old_photo_2x': {
        'model': "RealESRGAN_x2plus.pth",
        'face_restore': False,
        'recolor': True,
    },
    'old_photo_4x': {
        'model': "RealESRGAN_x4plus.pth",
        'face_restore': False,
        'recolor': True,
        'support_restore': True
    },
    'rembg_4x': {
        'model': ""
    },
    'rembg_2x': {
        'model': ""
    }
}
