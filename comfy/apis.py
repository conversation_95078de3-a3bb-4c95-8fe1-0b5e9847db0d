import json
import os
from functools import lru_cache

import requests

from conf.settings import DEFAULT_CLIENT_ID
from utils.lock_utils import lock_call_api
from utils.utils import get_seed

i2i_funcname_dict = {
    'restore': 'get_prompt_restore',  # 修复+上色
    'rembg': 'get_prompt_rembg',  # 去背景
    'bg_blur': 'get_prompt_rembg',  # 背景虚化
    'upscale': 'get_prompt_upscale',  # 图片放大
    'enhance': 'get_prompt_enhance',  # 图片增强
    'passport': 'get_prompt_passport',  # 证件照
    'headshot': 'get_prompt_headshot',  # 头像
    'face_crop': 'get_prompt_face_crop',  # 人脸裁剪
    'face_swap': 'get_prompt_face_swap',  # 人脸换脸
    'object_remove': 'get_prompt_object_remove',  # 物体移除
    'text_remove': 'get_prompt_text_remove',  # 文字移除
    'ai_filter': 'get_prompt_ai_filter',  # ai滤镜
    'ai_baby': 'get_prompt_ai_baby',  # ai baby
    'object_swap': 'get_prompt_object_swap',  # 换物
    'hair_swap': 'get_prompt_hair_swap',  # 换发型
    'ai_bg': 'get_prompt_ai_bg',  # ai换背景
    'ai_enlarge': 'get_prompt_ai_enlarge',  # ai扩图
    'ai_skin_repair': 'get_prompt_ai_skin_repair',  # ai皮肤修复
    'ai_recolor': 'get_prompt_ai_recolor',  # ai上色
    'ai_face_cutout': 'get_prompt_ai_face_cutout',  # ai裁脸
    'try_on': 'get_prompt_try_on',  # ai换衣
    'ai_wardrobe': 'get_prompt_ai_wardrobe',
    'ai_filter_ghibli': 'get_prompt_ai_filter_ghibli',
    'headshot_pro': 'get_headshot_pro',
    'face_detect': 'get_face_detect',
}

t2i_funcname_dict = {
    'art_v1': 'get_prompt_art_v1'
}

# 类型对应的函数名
type_funcname_dict = {}
type_funcname_dict.update(i2i_funcname_dict)
type_funcname_dict.update(t2i_funcname_dict)


class BaseComfyUi(object):

    def __init__(self, server_address, **kwargs):
        self.prompt_id = ""
        self.prompt = ""
        self.server_address = server_address
        self.client_id = DEFAULT_CLIENT_ID
        self.timeout = 30
        self.params = kwargs
        self.number = kwargs.get('priority', None)  # 优先级
        self.img_url = kwargs.get('img_url', "")
        self.task_id = kwargs.get('task_id', "")
        self.mq_instance = kwargs.get('mq_instance', None)
        self.kwargs = kwargs
        self.lock_key = f"lock:{self.server_address}"

    def queue_prompt(self, prompt):
        """
        任务推向队列
        :param prompt:
        :return:
        """
        data = {"prompt": prompt, "client_id": self.client_id}
        if self.number is not None:
            data["number"] = self.number
        url = f"http://{self.server_address}/prompt"
        req = lock_call_api(url=url, lock_key=self.lock_key, data=data, timeout=self.timeout)
        return req.json()

    def upload_image_by_url(self, img_url=None):
        """
        默认传img_url
        :param img_url:
        :return:
        """
        if img_url is None:
            img_url = self.img_url
        data = {'img_url': img_url}
        response = requests.post(f'http://{self.server_address}/upload/by_url_image', json=data, timeout=self.timeout)
        response.raise_for_status()
        return response.json()

    @staticmethod
    def get_base_prompt(filename: str) -> dict:
        dir_name = os.path.dirname(__file__)
        file_path = os.path.join(dir_name, 'workflows', filename)
        with open(file_path, encoding='utf-8') as f:
            prompt = json.load(f)
        return prompt


class ImgGeneratorToolComfy(BaseComfyUi):
    """
    图生图工具类
    """

    def gen(self, **kwargs):
        func_name = type_funcname_dict.get(kwargs.get('func_type'))
        prompt_func = getattr(self, func_name, None)
        prompt = prompt_func(file_name=self.img_url, **kwargs)
        self.prompt_id = self.queue_prompt(prompt)['prompt_id']
        self.prompt = prompt
        return self.prompt_id

    def get_prompt_restore(self, *, file_name, **kwargs):
        """
        修复
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('restore.json')
        restore = kwargs.get('restore')  # 修复开关
        recolor = kwargs.get('recolor')  # 上色开关
        prompt['16']['inputs']['image'] = file_name
        if restore is not None:
            prompt['11']['inputs']['enabled'] = restore
        if recolor is not None:
            prompt['15']['inputs']['enabled'] = recolor
        return prompt

    def get_prompt_rembg(self, *, file_name, **kwargs):
        """
        去背景
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('rembg.json')
        prompt['3']['inputs']['image'] = file_name
        self.prompt = prompt
        return prompt

    def get_prompt_upscale(self, *, file_name, **kwargs):
        """
        图片放大
        :param file_name:
        :param kwargs:
        :return:
        """
        size = kwargs.get('size', 2)
        prompt = self.get_base_prompt('upscale_api.json')
        prompt['10']['inputs']['image'] = file_name
        prompt['16']['inputs']['scale_by'] = float(size) / 4
        return prompt

    def get_prompt_enhance(self, *, file_name, **kwargs):
        """
        图片增强
        :param file_name:
        :param kwargs:
        :return:
        """
        mode = kwargs.get('mode')
        if mode == 'rembg':
            # 是否是去背景超分
            size = kwargs.get('size', '')
            file = 'rembg_enhance.json'
            prompt = self.get_base_prompt(file)
            prompt['64']['inputs']['image'] = file_name
            if str(size) == '2':
                prompt['28']['inputs']['upscale_model'] = 'x2'
                prompt['28']['inputs']['upscale'] = 2
                prompt['76']['inputs']['scale_by'] = 2
            elif str(size) == '4':
                prompt['28']['inputs']['upscale_model'] = 'x4'
                prompt['28']['inputs']['upscale'] = 4
                prompt['76']['inputs']['scale_by'] = 4
            return prompt

        face_restore = kwargs.get('face_restore')  # 修复开关
        if face_restore:
            # 脸部修复使用restore的json
            file = 'restore.json'
            prompt = self.get_base_prompt(file)
            recolor = kwargs.get('recolor')  # 上色开关
            prompt['16']['inputs']['image'] = file_name
            prompt['11']['inputs']['enabled'] = True
            prompt['11']['inputs']['upscale'] = 4
            prompt['11']['inputs']['upscale_model'] = "x4"
            prompt['11']['inputs']['codeformer_fidelity'] = 0.8
            if recolor is not None:
                prompt['15']['inputs']['enabled'] = recolor
            return prompt
        else:
            file = 'enhance.json'
            prompt = self.get_base_prompt(file)
            model = kwargs.get('model')  # upscale模型名
            recolor = kwargs.get('recolor')  # 上色开关
            prompt['15']['inputs']['image'] = file_name
            if model is not None:
                prompt['21']['inputs']['model_name'] = model

            if face_restore is not None:
                prompt['23']['inputs']['enabled'] = face_restore

            if recolor is not None:
                prompt['37']['inputs']['enabled'] = recolor
            return prompt

    def get_prompt_passport(self, *, file_name, **kwargs):
        """
        新版passport
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('passport_photo_maker.json')
        prompt['3']['inputs']['image'] = file_name
        return prompt

    def get_prompt_headshot(self, *, file_name, **kwargs):
        """
        新版headshot
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('headshot.json')

        seed = kwargs.get('seed', get_seed())
        batch_size = kwargs.get('batch_size', 1)
        width = kwargs.get('width')
        height = kwargs.get('height')
        cfg = kwargs.get('cfg')
        step = kwargs.get('steps')
        sampler_name = kwargs.get('sampler_name')
        scheduler = kwargs.get('scheduler')
        pulid_method = kwargs.get('pulid_method')
        pulid_weight = kwargs.get('pulid_weight')
        text = kwargs.get('prompt')
        negative_text = kwargs.get('negative_prompt')

        prompt['3']['inputs'].update({
            'seed': seed,
            'cfg': cfg,
            'steps': step,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
        })
        prompt['5']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size,
        })
        prompt['33']['inputs'].update({
            'method': pulid_method,
            'weight': pulid_weight,
        })

        prompt['12']['inputs']['image'] = file_name

        prompt['22']['inputs']['text'] = text
        prompt['23']['inputs']['text'] = negative_text
        return prompt

    def get_prompt_face_crop(self, *, file_name, **kwargs):
        """
        人脸裁剪
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('face_crop.json')
        prompt['15']['inputs']['image'] = file_name
        return prompt

    def get_prompt_face_swap(self, *, file_name, **kwargs):
        """
        换脸
        :param file_name:
        :param kwargs:
        :return:
        """
        input_index = kwargs.get('input_index')
        source_index = kwargs.get('source_index')
        source_urls = kwargs.get('source_urls')
        prompt = self.get_base_prompt('face_swap.json')
        prompt['15']['inputs']['image'] = file_name
        prompt['13']['inputs']['input_faces_index'] = input_index
        prompt['13']['inputs']['source_faces_index'] = source_index
        prompt['14']['inputs']['url_dir'] = source_urls
        return prompt

    def get_prompt_text_remove(self, *, file_name, **kwargs):
        """
        去水印
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('text_remove.json')
        seed = kwargs.get('seed', get_seed())
        prompt['71']['inputs']['image'] = file_name
        prompt['33']['inputs']['seed'] = seed
        return prompt

    def get_prompt_object_remove(self, *, file_name, **kwargs):
        """
        局部去物
        :param file_name:
        :param kwargs:
        :return:
        """
        mask_url = kwargs.get('mask_url', '')
        custom = kwargs.get('custom', '')
        seed = kwargs.get('seed', get_seed())
        if mask_url:
            prompt = self.get_base_prompt('object_remove_mask.json')
            prompt['11']['inputs']['image'] = file_name
            prompt['103']['inputs']['image'] = mask_url
            prompt['3']['inputs']['seed'] = seed
        else:
            prompt = self.get_base_prompt('object_remove_detect.json')
            prompt['105']['inputs']['image'] = file_name
            prompt['131']['inputs']['seed'] = seed
            prompt['108']['inputs']['prompt'] = custom
        return prompt

    def get_prompt_ai_filter(self, *, file_name, **kwargs):
        """
        ai滤镜
        :param file_name:
        :param kwargs:
        :return:
        """
        batch_size = kwargs.get('batch_size', 1)
        workflow_type = kwargs.get('workflow_type', '')
        seed = kwargs.get('seed', get_seed())
        if 'labubu_filter' == workflow_type:
            prompt = self.get_base_prompt('labubu_filter.json')
            prompt['17']['inputs']['image'] = file_name
            prompt['68']['inputs']['amount'] = batch_size
            prompt['155']['inputs']['seed'] = seed
            return prompt
        prompt = self.get_base_prompt('ai_filter.json')

        base_model = kwargs.get('base_model')
        lora = kwargs.get('lora')

        cfg = kwargs.get('cfg')
        denoise = kwargs.get('denoise')

        strength_model = kwargs.get('strength_model')
        strength_clip = kwargs.get('strength_clip')

        text_a = kwargs.get('text_a')
        text_c = kwargs.get('text_c')

        cn_strength = kwargs.get('cn_strength')
        cn_start = kwargs.get('cn_start')
        cn_end = kwargs.get('cn_end')

        negative_text = kwargs.get('negative_prompt')

        task_type = kwargs.get('t_type', 'caption')
        task_token = kwargs.get('task_token', 1024)

        prompt['7']['inputs']['text'] = negative_text  # 负向提示词

        prompt['3']['inputs'].update({
            'seed': seed,
            'cfg': cfg,
            'denoise': denoise
        })

        prompt['71']['inputs']['task'] = task_type
        prompt['71']['inputs']['max_new_tokens'] = task_token

        prompt['21']['inputs']['image'] = file_name

        prompt['69']['inputs']['text_a'] = text_a
        prompt['69']['inputs']['text_c'] = text_c

        prompt['27']['inputs']['ckpt_name'] = base_model  # 基础模型名字

        # lora配置
        prompt['26']['inputs'].update({
            'lora_name': lora,
            'strength_model': strength_model,
            'strength_clip': strength_clip
        })
        # ControINet配置
        prompt['28']['inputs'].update({
            'strength': cn_strength,
            'start_percent': cn_start,
            'end_percent': cn_end
        })
        prompt['64']['inputs']['amount'] = batch_size
        return prompt

    def get_prompt_ai_baby(self, *, file_name, **kwargs):
        """
        ai baby
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('ai_baby.json')

        seed = kwargs.get('seed', get_seed())

        prompt_text = kwargs.get('prompt')

        weight = kwargs.get('weight')

        parent2 = kwargs.get('parent2')

        img1_weight = kwargs.get('img1_weight', 0.5)
        img2_weight = kwargs.get('img2_weight', 0.5)

        prompt['19']['inputs']['text'] = prompt_text  # 正向提示词
        prompt['139']['inputs']['weight'] = weight  # 权重

        prompt['17']['inputs']['seed'] = seed

        prompt['10']['inputs']['image'] = file_name  # parent1
        prompt['11']['inputs']['image'] = parent2  # parent2
        prompt['137']['inputs']['value'] = img1_weight
        prompt['138']['inputs']['value'] = img2_weight
        return prompt

    def get_prompt_object_swap(self, *, file_name, **kwargs):
        """
        object swap
        :param file_name:
        :param kwargs:
        :return:
        """
        seed = kwargs.get('seed', get_seed())
        mask_url = kwargs.get('mask_url')
        source = kwargs.get('source')
        target = kwargs.get('target')
        batch_size = kwargs.get('batch_size', 2)

        if mask_url:
            prompt = self.get_base_prompt('object_swap_mask.json')
            prompt['3']['inputs']['seed'] = seed
            prompt['6']['inputs']['text'] = target
            prompt['71']['inputs']['image'] = mask_url
            prompt['11']['inputs']['image'] = file_name
            prompt['91']['inputs']['amount'] = batch_size
        else:
            prompt = self.get_base_prompt('object_swap_detect.json')
            prompt['100']['inputs']['prompt'] = source
            prompt['6']['inputs']['text'] = target
            prompt['3']['inputs']['seed'] = seed
            prompt['96']['inputs']['image'] = file_name
            prompt['91']['inputs']['amount'] = batch_size

        return prompt

    def get_prompt_hair_swap(self, *, file_name, **kwargs):
        """
        object swap
        :param file_name:
        :param kwargs:
        :return:
        """
        seed = kwargs.get('seed', get_seed())
        style_prompt = kwargs.get('style_prompt')
        color_prompt = kwargs.get('color_prompt')
        if style_prompt and 'Bald Hair' in style_prompt:
            prompt = self.get_base_prompt('bald_hair.json')
            prompt['12']['inputs']['image'] = file_name
            prompt['44']['inputs']['seed'] = seed
        else:
            prompt = self.get_base_prompt('hair_swap.json')
            prompt['12']['inputs']['image'] = file_name
            prompt['44']['inputs']['seed'] = seed
            prompt['1']['inputs']['seed'] = seed
            prompt['107']['inputs']['string'] = style_prompt
            prompt['108']['inputs']['string'] = color_prompt
        return prompt

    def get_prompt_ai_bg(self, *, file_name, **kwargs):
        """
        ai bg
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('ai_bg.json')
        seed = kwargs.get('seed', get_seed())
        positive_prompt = kwargs.get('prompt')
        negative_prompt = kwargs.get('negative_prompt')
        prompt['131']['inputs']['image'] = file_name
        prompt['127']['inputs']['seed'] = seed
        prompt['128']['inputs']['text'] = positive_prompt
        if negative_prompt:
            prompt['129']['inputs']['text'] = negative_prompt
        return prompt

    def get_prompt_ai_enlarge(self, *, file_name, **kwargs):
        """
        :param file_name:
        :param kwargs:
        :return:
        """
        prompt = self.get_base_prompt('ai_enlarge.json')
        seed = kwargs.get('seed', get_seed())
        position = kwargs.get('position', {})
        prompt['10']['inputs'].update(position)
        prompt['3']['inputs']['seed'] = seed
        prompt['11']['inputs']['image'] = file_name
        return prompt

    def get_prompt_ai_skin_repair(self, *, file_name, **kwargs):
        """
        ai skin repair
        :param file_name:
        :param kwargs:
        :return:
        """
        seed = kwargs.get('seed', get_seed())
        mask_url = kwargs.get('mask_url')
        if mask_url:
            prompt = self.get_base_prompt('ai_skin_repair_mask.json')
            prompt['3']['inputs']['seed'] = seed
            prompt['1']['inputs']['image'] = file_name
            prompt['33']['inputs']['image'] = mask_url

        else:
            prompt = self.get_base_prompt('ai_skin_repair_detect.json')
            prompt['1']['inputs']['image'] = file_name

        return prompt

    def get_prompt_ai_recolor(self, *, file_name, **kwargs):
        """
        ai skin repair
        :param file_name:
        :param kwargs:
        :return:
        """
        seed = kwargs.get('seed', get_seed())
        position = kwargs.get('position', '')
        color = kwargs.get('color', '')
        mask_url = kwargs.get('mask_url', '')
        if mask_url:
            prompt = self.get_base_prompt('ai_recolor_mask.json')
            prompt['3']['inputs']['seed'] = seed
            prompt['12']['inputs']['image'] = file_name
            prompt['253']['inputs']['string'] = color
            prompt['268']['inputs']['image'] = mask_url
        else:
            prompt = self.get_base_prompt('ai_recolor.json')
            prompt['3']['inputs']['seed'] = seed
            prompt['12']['inputs']['image'] = file_name
            prompt['253']['inputs']['string'] = position
            prompt['257']['inputs']['Prepend'] = color

        return prompt

    # 以下是文生图
    def get_prompt_art_v1(self, **kwargs):
        """
        :param file_name:
        :param kwargs:
        :return:
        """


        text = kwargs.get('prompt', '')
        width = kwargs.get('width', 1024)
        height = kwargs.get('height', 1024)
        batch_size = kwargs.get('batch_size', 2)
        workflow_type = kwargs.get('workflow_type', '')
        ref_weight = kwargs.get('ref_weight', 0.8)
        ref_img = kwargs.get('ref_img', '')
        seed = kwargs.get('seed', get_seed())
        match workflow_type:
            case 'art_character_ref':
                prompt = self.get_base_prompt('art_character_ref.json')
                prompt['83']['inputs']['image'] = ref_img
                prompt['73']['inputs']['seed'] = seed
                prompt['71']['inputs']['text'] = text
                prompt['86']['inputs'].update({
                    'width': width,
                    'height': height,
                    'batch_size': batch_size
                })
                prompt['87']['inputs']['weight'] = ref_weight
            case 'art_content_ref':
                prompt = self.get_base_prompt('art_content_ref.json')
                prompt['17']['inputs']['image'] = ref_img
                prompt['113']['inputs']['seed'] = seed
                prompt['122']['inputs']['text_a'] = text

                prompt['68']['inputs']['amount'] = batch_size
                prompt['78']['inputs']['value'] = ref_weight * 100
            case 'labubu':
                prompt = self.get_base_prompt('labubu.json')
                prompt['59']['inputs']['seed'] = seed
                prompt['65']['inputs']['text_b'] = text
                prompt['27']['inputs'].update({
                    'width': width,
                    'height': height,
                    'batch_size': batch_size
                })
            case _:
                prompt = self.get_base_prompt('flux_art_v1.json')
                prompt['59']['inputs']['seed'] = seed
                prompt['6']['inputs']['text'] = text
                prompt['72']['inputs'].update({
                    'width': width,
                    'height': height,
                    'batch_size': batch_size
                })

        return prompt

    def get_prompt_ai_face_cutout(self, *, file_name, **kwargs):
        """
        ai_face_cutout
        :param file_name:
        :param kwargs:
        :return:
        """

        prompt = self.get_base_prompt('ai_face_cutout.json')
        prompt['3']['inputs']['image'] = file_name
        return prompt

    def get_prompt_try_on(self, *, file_name, **kwargs):
        """
        try_on
        :param file_name:
        :param kwargs:
        :return:
        """

        prompt = self.get_base_prompt('try_on.json')

        seed = kwargs.get('seed', get_seed())

        garment_type = kwargs.get('garment_type')

        cloth_url = kwargs.get('cloth_url', '')

        prompt['22']['inputs']['seed'] = seed

        prompt['21']['inputs']['vt_garment_type'] = garment_type


        prompt['32']['inputs']['image'] = file_name
        prompt['34']['inputs']['image'] = cloth_url
        return prompt


    def get_prompt_ai_wardrobe(self, *, file_name, **kwargs):
        """
        ai_wardrobe
        :param file_name:
        :param kwargs:
        :return:
        """

        prompt = self.get_base_prompt('ai_wardrobe.json')

        seed = kwargs.get('seed', get_seed())

        garment_type = kwargs.get('garment_type')

        cloth_url = kwargs.get('cloth_url', '')

        prompt['3']['inputs']['seed'] = seed

        prompt['599']['inputs']['value'] = garment_type

        prompt['17']['inputs']['image'] = file_name
        prompt['54']['inputs']['image'] = cloth_url
        return prompt
    def get_prompt_ai_filter_ghibli(self, *, file_name, **kwargs):
        prompt = self.get_base_prompt('ai_filter_ghibli.json')
        prompt['20']['inputs']['image'] = file_name
        return prompt

    def get_headshot_pro(self, *, file_name, **kwargs):

        api_prompt = self.get_base_prompt('headshot_pro.json')
        seed = kwargs.get('seed', get_seed())
        prompt = kwargs.get('prompt')
        api_prompt['13']['inputs']['seed'] = seed
        api_prompt['30']['inputs']['seed'] = get_seed()
        api_prompt['76']['inputs']['string'] = prompt
        api_prompt['9']['inputs']['image'] = file_name
        api_prompt['88']['inputs']['value'] = 896
        api_prompt['89']['inputs']['value'] = 1088
        return api_prompt
    def get_face_detect(self, *, file_name, **kwargs):
        api_prompt = self.get_base_prompt('face_detect.json')
        api_prompt['2']['inputs']['image'] = file_name
        return api_prompt

if __name__ == '__main__':
    ...
