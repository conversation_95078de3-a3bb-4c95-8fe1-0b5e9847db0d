{"3": {"inputs": {"text": ["253", 0], "clip": ["92", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "4": {"inputs": {"text": "text, watermark", "clip": ["92", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Nagative Prompt)"}}, "12": {"inputs": {"image": "2a546c73-7caf-42e7-89b3-b28ad526411b.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "14": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["12", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "30": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "31": {"inputs": {"type": "repaint", "control_net": ["30", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "44": {"inputs": {"seed": 1076951695185185, "steps": 15, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["92", 0], "positive": ["45", 0], "negative": ["45", 1], "latent_image": ["55", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "45": {"inputs": {"strength": 0.15, "start_percent": 0, "end_percent": 1, "positive": ["219", 0], "negative": ["219", 1], "control_net": ["31", 0], "image": ["52", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Inpainting ControlNet"}}, "47": {"inputs": {"mask": ["57", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Mask to Image"}}, "48": {"inputs": {"samples": ["44", 0], "vae": ["92", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode(Ouput)"}}, "52": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["14", 0], "source": ["47", 0], "mask": ["271", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "55": {"inputs": {"amount": 1, "samples": ["226", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "57": {"inputs": {"mask": ["271", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "92": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "93": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["48", 0], "get_image_size": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "100": {"inputs": {"filename_prefix": "aiease_recolor", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["261", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "219": {"inputs": {"strength": 0.9, "start_percent": 0, "end_percent": 1, "positive": ["3", 0], "negative": ["4", 0], "control_net": ["221", 0], "image": ["14", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Edge ControlNet"}}, "221": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["30", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "226": {"inputs": {"grow_mask_by": 30, "pixels": ["14", 0], "vae": ["92", 2], "mask": ["271", 0]}, "class_type": "VAEEncodeForInpaint", "_meta": {"title": "VAE Encode (for Inpainting)"}}, "253": {"inputs": {"string": "blue"}, "class_type": "StringConstant", "_meta": {"title": "String Constant: Detect Object"}}, "261": {"inputs": {"image_from": ["12", 0], "image_to": ["93", 0], "mask": ["265", 0]}, "class_type": "ImageCompositeFromMaskBatch+", "_meta": {"title": "🔧 Image Composite From Mask Batch"}}, "265": {"inputs": {"expand": 10, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 8, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["268", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "268": {"inputs": {"image": "2a546c73-7caf-42e7-89b3-b28ad526411b.webp", "channel": "red", "upload": "image"}, "class_type": "AIEaseLoadImageMask", "_meta": {"title": "Aiease Load Image (as <PERSON>)"}}, "271": {"inputs": {"width": ["274", 0], "height": ["274", 1], "keep_proportions": false, "mask": ["268", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "274": {"inputs": {"image": ["14", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}}