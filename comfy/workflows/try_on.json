{"17": {"inputs": {}, "class_type": "MASK_PROCESS_MODEL", "_meta": {"title": "leffa mask model load"}}, "21": {"inputs": {"vt_garment_type": "upper_body", "control_type": "virtual_tryon", "vt_model_type": "viton_hd", "pipe": ["17", 0], "image": ["32", 0]}, "class_type": "MASK_PROCESS", "_meta": {"title": "leffa mask process"}}, "22": {"inputs": {"steps": 20, "cfg": 2.5, "seed": 557918, "pipe": ["23", 0], "model": ["32", 0], "cloth": ["34", 0], "pose": ["24", 0], "mask": ["30", 0]}, "class_type": "AIEASE_Leffa_Viton_Run", "_meta": {"title": "AIEASE_Leffa_Viton_Run"}}, "23": {"inputs": {"model": "franciszzj/Leffa", "viton_type": "hd"}, "class_type": "AIEASE_Leffa_Viton_Load", "_meta": {"title": "AIEASE_Leffa_Viton_Load"}}, "24": {"inputs": {"control_type": "virtual_tryon", "vt_model_type": "viton_hd", "pipe": ["25", 0], "image": ["32", 0]}, "class_type": "DENSEPOSE_PROCESS", "_meta": {"title": "densepose process"}}, "25": {"inputs": {}, "class_type": "DENSEPOSE_MODEL", "_meta": {"title": "densepose model load"}}, "27": {"inputs": {"image": ["32", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "28": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["27", 0], "height": ["27", 1], "crop": "disabled", "image": ["22", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "30": {"inputs": {"expand": 8, "tapered_corners": true, "mask": ["21", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "32": {"inputs": {"image": "https://images.piclumen.com/normal/20250125/11/a4831ab1448b4881a949a840bc028b3a.webp"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "AIEaseLoadImage"}}, "34": {"inputs": {"image": "https://images.piclumen.com/normal/20250311/16/c25d8c69aeed498c86de5e389521d92f.webp"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "AIEaseLoadImageClothes"}}, "35": {"inputs": {"filename_prefix": "aiease_try_on", "nsfw_detect": false, "file_type": "WEBP (lossless)", "images": ["28", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}