{"3": {"inputs": {"seed": 947329024545833, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["75", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["91", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": "", "clip": ["75", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "NSFW, watermask", "clip": ["75", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["75", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "11": {"inputs": {"image": "input.png", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "15": {"inputs": {"strength": 0.2, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["54", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "repaint", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "48": {"inputs": {"mask": ["50", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "50": {"inputs": {"mask": ["72", 0]}, "class_type": "InvertMask", "_meta": {"title": "Invert Mask"}}, "54": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["76", 0], "source": ["48", 0], "mask": ["72", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "64": {"inputs": {"amount": 4, "mask": ["72", 0]}, "class_type": "MaskBlur+", "_meta": {"title": "🔧 Mask Blur"}}, "70": {"inputs": {"grow_mask_by": 30, "pixels": ["54", 0], "vae": ["75", 2], "mask": ["72", 0]}, "class_type": "VAEEncodeForInpaint", "_meta": {"title": "VAE Encode (for Inpainting)"}}, "71": {"inputs": {"image": "mask.png", "channel": "red", "upload": "image"}, "class_type": "AIEaseLoadImageMask", "_meta": {"title": "Aiease Load Image (as <PERSON>)"}}, "72": {"inputs": {"grow": 4, "blur": 0, "mask": ["77", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "75": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "76": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["11", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "77": {"inputs": {"width": ["85", 0], "height": ["85", 1], "keep_proportions": false, "mask": ["71", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "83": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["86", 0], "height_input": ["86", 1], "crop": "disabled", "image": ["8", 0], "get_image_size": ["11", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "84": {"inputs": {"width": ["86", 0], "height": ["86", 1], "keep_proportions": false, "mask": ["64", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "85": {"inputs": {"image": ["76", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "86": {"inputs": {"image": ["11", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "91": {"inputs": {"amount": 2, "samples": ["70", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "98": {"inputs": {"filename_prefix": "aiease_object_swap_mask", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["83", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}