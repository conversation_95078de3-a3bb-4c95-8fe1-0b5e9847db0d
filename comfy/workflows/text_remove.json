{"33": {"inputs": {"seed": 878580414538651, "steps": 15, "cfg": 4.5, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["78", 0], "positive": ["39", 0], "negative": ["39", 1], "latent_image": ["80", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "35": {"inputs": {"text": "pure background, smooth, no human, no person, no characters, no objects, no items, no watermark, no subjects, clear", "clip": ["78", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "36": {"inputs": {"text": "text,watermark", "clip": ["78", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "37": {"inputs": {"samples": ["33", 0], "vae": ["78", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "39": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["35", 0], "negative": ["36", 0], "control_net": ["41", 0], "image": ["49", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "40": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "41": {"inputs": {"type": "repaint", "control_net": ["40", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "47": {"inputs": {"mask": ["48", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "48": {"inputs": {"mask": ["59", 0]}, "class_type": "InvertMask", "_meta": {"title": "Invert Mask"}}, "49": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["63", 0], "source": ["47", 0], "mask": ["59", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "52": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["71", 0], "source": ["69", 0], "mask": ["59", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "56": {"inputs": {"fill": "telea", "falloff": 0, "image": ["63", 0], "mask": ["59", 0]}, "class_type": "INPAINT_MaskedFill", "_meta": {"title": "Fill Masked Area"}}, "59": {"inputs": {"grow": 6, "blur": 0, "mask": ["62", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "62": {"inputs": {"width": ["64", 0], "height": ["64", 1], "keep_proportions": false, "mask": ["73", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "63": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["64", 0], "height_input": ["64", 1], "crop": "disabled", "image": ["71", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "64": {"inputs": {"image": ["71", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "66": {"inputs": {"image": ["71", 0]}, "class_type": "ImageGenResolutionFromImage", "_meta": {"title": "Generation Resolution From Image"}}, "69": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["66", 0], "height_input": ["66", 1], "crop": "disabled", "image": ["37", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "71": {"inputs": {"image": "2.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "72": {"inputs": {"model_checkpoint": "hi_sam_h.pth", "device": "cuda"}, "class_type": "HiSAMModelInit", "_meta": {"title": "Hi-SAM Model Init"}}, "73": {"inputs": {"thres_input": 0, "patch_mode": "disable", "hier_det": "disable", "input_image": ["71", 0], "model": ["72", 0]}, "class_type": "HiSAMInference", "_meta": {"title": "Hi-SAM Inference"}}, "78": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "80": {"inputs": {"grow_mask_by": 30, "pixels": ["56", 0], "vae": ["78", 2], "mask": ["59", 0]}, "class_type": "VAEEncodeForInpaint", "_meta": {"title": "VAE Encode (for Inpainting)"}}, "81": {"inputs": {"filename_prefix": "aiease_text_remove", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["52", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}