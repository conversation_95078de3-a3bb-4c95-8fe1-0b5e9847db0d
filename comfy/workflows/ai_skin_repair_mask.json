{"1": {"inputs": {"image": "", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "2": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "3": {"inputs": {"seed": 838407831402821, "steps": 15, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 0.41, "model": ["2", 0], "positive": ["4", 0], "negative": ["5", 0], "latent_image": ["7", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"text": "(Smooth skin:1.1), movie screen lighting,Flawless", "clip": ["2", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "5": {"inputs": {"text": "((wrinkle)), freckle, smallpox, bad hand，Blemishes，Pimples，Uneven texture,Pimples,Dark spots", "clip": ["2", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "6": {"inputs": {"samples": ["3", 0], "vae": ["2", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"samples": ["8", 0], "mask": ["20", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "Set Latent Noise Mask"}}, "8": {"inputs": {"pixels": ["31", 0], "vae": ["2", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "20": {"inputs": {"expand": -4, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 4, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["32", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "22": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["31", 0], "source": ["6", 0], "mask": ["23", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "23": {"inputs": {"expand": 2, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 2, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": true, "mask": ["32", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "27": {"inputs": {"image": ["1", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "28": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["27", 0], "height": ["27", 1], "crop": "disabled", "image": ["22", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "30": {"inputs": {"image": ["1", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "31": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["30", 0], "height": ["30", 1], "crop": "disabled", "image": ["1", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "32": {"inputs": {"width": ["30", 0], "height": ["30", 1], "keep_proportions": false, "mask": ["33", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "33": {"inputs": {"image": "", "channel": "red", "upload": "image"}, "class_type": "AIEaseLoadImageMask", "_meta": {"title": "Aiease Load Image (as <PERSON>)"}}, "36": {"inputs": {"filename_prefix": "aiease_ai_skin_repair", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["28", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}