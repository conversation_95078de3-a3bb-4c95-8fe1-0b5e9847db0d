{"3": {"inputs": {"text": "(woman bald head:1.6),young person, smooth bald head, (small head with background:1.6)", "clip": ["92", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码（提示）"}}, "4": {"inputs": {"text": "text, watermark, (hair:1.8), (fur:1.3),(big head:1.65),(side hair:1.8),(strands of hair:1.7)", "clip": ["92", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码（提示）"}}, "12": {"inputs": {"image": "2.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "14": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["12", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "将图像缩放到总像素"}}, "15": {"inputs": {"pixels": ["14", 0], "vae": ["92", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "37": {"inputs": {"mask1": ["295", 0], "mask2": ["242", 0]}, "class_type": "SubtractMask", "_meta": {"title": "Pixelwise(MASK - MASK)"}}, "44": {"inputs": {"seed": 2601829721, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["92", 0], "positive": ["45", 0], "negative": ["45", 1], "latent_image": ["55", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "45": {"inputs": {"strength": 0.55, "start_percent": 0, "end_percent": 0.85, "positive": ["3", 0], "negative": ["4", 0], "control_net": ["176", 0], "image": ["52", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "应用控制网"}}, "47": {"inputs": {"mask": ["57", 0]}, "class_type": "MaskToImage", "_meta": {"title": "将遮罩转换为图像"}}, "48": {"inputs": {"samples": ["44", 0], "vae": ["92", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "52": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["14", 0], "source": ["47", 0], "mask": ["294", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "图像合成遮罩"}}, "53": {"inputs": {"samples": ["202", 0], "mask": ["294", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "设置潜空间噪声遮罩"}}, "55": {"inputs": {"amount": 1, "samples": ["53", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "重复潜空间批处理"}}, "57": {"inputs": {"mask": ["294", 0]}, "class_type": "InvertMask", "_meta": {"title": "反转遮罩"}}, "74": {"inputs": {"expand": ["267", 0], "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 15, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["37", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "77": {"inputs": {"value": 2601829721, "mode": true, "action": "randomize", "last_seed": 4035742617}, "class_type": "GlobalSeed //Inspire", "_meta": {"title": "Global Seed (Inspire)"}}, "92": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "93": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["48", 0], "get_image_size": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "100": {"inputs": {"filename_prefix": "aiease_hair", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["93", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "141": {"inputs": {"threshold": 100, "mask": ["74", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "160": {"inputs": {"threshold": 100, "mask": ["301", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "175": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载控制网模型"}}, "176": {"inputs": {"type": "repaint", "control_net": ["175", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "设置联合控制网类型"}}, "194": {"inputs": {"width": ["277", 1], "height": ["277", 2], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空潜空间图像"}}, "202": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["15", 0], "source": ["194", 0], "mask": ["294", 0]}, "class_type": "LatentCompositeMasked", "_meta": {"title": "潜空间合成遮罩"}}, "224": {"inputs": {"library": "insightface", "provider": "CUDA"}, "class_type": "FaceAnalysisModels", "_meta": {"title": "Face Analysis Models"}}, "226": {"inputs": {"area": "face", "grow": 0, "grow_tapered": false, "blur": 13, "analysis_models": ["224", 0], "image": ["14", 0]}, "class_type": "FaceSegmentation", "_meta": {"title": "Face Segmentation"}}, "242": {"inputs": {"threshold": 100, "mask": ["226", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "265": {"inputs": {"output_type": "float", "masks": ["226", 0]}, "class_type": "MaskToWeightofImage", "_meta": {"title": "GetMaskRatio"}}, "267": {"inputs": {"expression": "round(a*200)", "a": ["265", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "277": {"inputs": {"image": ["14", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "290": {"inputs": {"area": "main_features", "grow": 0, "grow_tapered": false, "blur": 1, "analysis_models": ["224", 0], "image": ["14", 0]}, "class_type": "FaceSegmentation", "_meta": {"title": "Face Segmentation"}}, "291": {"inputs": {"mask1": ["141", 0], "mask2": ["292", 0]}, "class_type": "SubtractMask", "_meta": {"title": "Pixelwise(MASK - MASK)"}}, "292": {"inputs": {"threshold": 100, "mask": ["290", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "294": {"inputs": {"mask1": ["160", 0], "mask2": ["291", 0]}, "class_type": "AddMask", "_meta": {"title": "Avoid hair near eyes"}}, "295": {"inputs": {"grow": 30, "blur": 7, "mask": ["160", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "298": {"inputs": {"background": false, "face": false, "hair": true, "glasses": false, "top-clothes": false, "bottom-clothes": false, "torso-skin": false, "left-arm": false, "right-arm": false, "left-leg": false, "right-leg": false, "left-foot": false, "right-foot": false, "image": ["14", 0]}, "class_type": "HumanParts", "_meta": {"title": "Human Parts mask generator"}}, "300": {"inputs": {"text": "hair", "blur": 7, "threshold": 0.25, "dilation_factor": 7, "image": ["14", 0]}, "class_type": "CLIPSeg", "_meta": {"title": "CLIPSeg"}}, "301": {"inputs": {"mask1": ["306", 0], "mask2": ["300", 0]}, "class_type": "AddMask", "_meta": {"title": "Pixelwise(MASK + MASK)"}}, "302": {"inputs": {"grow": 7, "blur": 7, "mask": ["298", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "306": {"inputs": {"threshold": 100, "mask": ["302", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}}