{"6": {"inputs": {"text": "pure background, smooth, no human, no person, no characters, no objects, no items, no watermark, no subjects, clear", "clip": ["97", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "text, watermark", "clip": ["97", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "15": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["54", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "repaint", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "48": {"inputs": {"mask": ["50", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "50": {"inputs": {"mask": ["86", 0]}, "class_type": "InvertMask", "_meta": {"title": "Invert Mask"}}, "54": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["87", 0], "source": ["48", 0], "mask": ["86", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "71": {"inputs": {"fill": "telea", "falloff": 10, "image": ["87", 0], "mask": ["79", 0]}, "class_type": "INPAINT_MaskedFill", "_meta": {"title": "Fill Masked Area"}}, "75": {"inputs": {"pixels": ["71", 0], "vae": ["97", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "79": {"inputs": {"grow": 6, "blur": 0, "mask": ["86", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "86": {"inputs": {"width": ["89", 0], "height": ["89", 1], "keep_proportions": false, "mask": ["110", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "87": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["89", 0], "height_input": ["89", 1], "crop": "disabled", "image": ["105", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "89": {"inputs": {"image": ["105", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "92": {"inputs": {"image": ["105", 0]}, "class_type": "ImageGenResolutionFromImage", "_meta": {"title": "Generation Resolution From Image"}}, "95": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["92", 0], "height_input": ["92", 1], "crop": "disabled", "image": ["132", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "97": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "105": {"inputs": {"image": "3.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "106": {"inputs": {"model_name": "GroundingDINO_SwinB (938MB)"}, "class_type": "GroundingDinoModelLoader (segment anything2)", "_meta": {"title": "GroundingDinoModelLoader (segment anything2)"}}, "107": {"inputs": {"model_name": "sam2_hiera_large.pt"}, "class_type": "SAM2ModelLoader (segment anything2)", "_meta": {"title": "SAM2ModelLoader (segment anything2)"}}, "108": {"inputs": {"prompt": "", "threshold": 0.3, "sam_model": ["107", 0], "grounding_dino_model": ["106", 0], "image": ["105", 0]}, "class_type": "GroundingDinoSAM2Segment (segment anything2)", "_meta": {"title": "GroundingDinoSAM2Segment (segment anything2)"}}, "109": {"inputs": {"mask": ["108", 1]}, "class_type": "IsMaskEmptyNode", "_meta": {"title": "IsMaskEmpty-AIEase"}}, "110": {"inputs": {"dilation": 30, "mask": ["109", 0]}, "class_type": "ImpactDilateMask", "_meta": {"title": "Dilate Mask"}}, "121": {"inputs": {"image_from": ["105", 0], "image_to": ["95", 0], "mask": ["128", 0]}, "class_type": "ImageCompositeFromMaskBatch+", "_meta": {"title": "🔧 Image Composite From Mask Batch"}}, "126": {"inputs": {"amount": 1, "samples": ["75", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "128": {"inputs": {"expand": 30, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 12, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["86", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "131": {"inputs": {"add_noise": "enable", "noise_seed": 878580414538651, "steps": 20, "cfg": 4.5, "sampler_name": "euler", "scheduler": "normal", "start_at_step": 4, "end_at_step": 10000, "return_with_leftover_noise": "disable", "model": ["97", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["126", 0]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Advanced)"}}, "132": {"inputs": {"samples": ["131", 0], "vae": ["97", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "136": {"inputs": {"filename_prefix": "aiease_object_remove_detect", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["121", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}