{"35": {"inputs": {"seed": 1088431587084537, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["46", 0], "positive": ["101", 0], "negative": ["45", 1], "latent_image": ["45", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "36": {"inputs": {"text": "", "clip": ["44", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "37": {"inputs": {"samples": ["35", 0], "vae": ["43", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "40": {"inputs": {"text": "The person in the right picture wears the clothes on the left", "clip": ["44", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "41": {"inputs": {"guidance": 30, "conditioning": ["40", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "42": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e5m2"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "43": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "44": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "45": {"inputs": {"noise_mask": false, "positive": ["41", 0], "negative": ["36", 0], "vae": ["43", 0], "pixels": ["88", 0], "mask": ["103", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "46": {"inputs": {"model": ["42", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "83": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "84": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "88": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["139", 0], "image2": ["127", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "92": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["141", 0], "image2": ["146", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "101": {"inputs": {"strength": 1, "strength_type": "multiply", "conditioning": ["45", 0], "style_model": ["83", 0], "clip_vision_output": ["104", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "103": {"inputs": {"channel": "red", "image": ["92", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "104": {"inputs": {"crop": "center", "clip_vision": ["84", 0], "image": ["139", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "124": {"inputs": {"image": ["154", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "127": {"inputs": {"width": 512, "height": 512, "upscale_method": "bicubic", "keep_proportion": false, "divisible_by": 2, "width_input": ["124", 0], "height_input": ["124", 1], "crop": "disabled", "image": ["154", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "139": {"inputs": {"width": 512, "height": 512, "upscale_method": "bicubic", "keep_proportion": false, "divisible_by": 2, "width_input": ["124", 0], "height_input": ["124", 1], "crop": "disabled", "image": ["156", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "141": {"inputs": {"panel_width": ["139", 1], "panel_height": ["139", 2], "fill_color": "custom", "fill_color_hex": "#000000"}, "class_type": "CR Color Panel", "_meta": {"title": "🌁 CR Color Panel"}}, "143": {"inputs": {"vt_garment_type": "dresses", "control_type": "virtual_tryon", "vt_model_type": "viton_hd", "pipe": ["144", 0], "image": ["127", 0]}, "class_type": "MASK_PROCESS", "_meta": {"title": "leffa mask process"}}, "144": {"inputs": {}, "class_type": "MASK_PROCESS_MODEL", "_meta": {"title": "leffa mask model load"}}, "146": {"inputs": {"mask": ["149", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "149": {"inputs": {"grow": 13, "blur": 7, "mask": ["143", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "154": {"inputs": {"image": ""}, "class_type": "AIEaseLoadImage", "_meta": {"title": "AIEaseLoadImage"}}, "155": {"inputs": {"filename_prefix": "aiease_redux_try_on", "nsfw_detect": false, "file_type": "WEBP (lossless)", "images": ["157", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "156": {"inputs": {"image": ""}, "class_type": "AIEaseLoadImage", "_meta": {"title": "AIEaseLoadImage_cloths"}}, "157": {"inputs": {"images": ["37", 0]}, "class_type": "SplitRightImage", "_meta": {"title": "SplitRightImage"}}}