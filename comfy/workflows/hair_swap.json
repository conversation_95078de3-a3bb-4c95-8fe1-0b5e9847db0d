{"1": {"inputs": {"seed": 3752507893, "steps": 10, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["92", 0], "positive": ["29", 0], "negative": ["29", 1], "latent_image": ["27", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "3": {"inputs": {"text": ["61", 0], "clip": ["92", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "4": {"inputs": {"text": "text, watermark", "clip": ["92", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "5": {"inputs": {"samples": ["1", 0], "vae": ["92", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "12": {"inputs": {"image": "man2.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "14": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["12", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "ImageScaleToTotalPixels"}}, "15": {"inputs": {"pixels": ["14", 0], "vae": ["92", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "26": {"inputs": {"mask": ["177", 0]}, "class_type": "InvertMask", "_meta": {"title": "Invert Mask"}}, "27": {"inputs": {"samples": ["15", 0], "mask": ["26", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "Set Latent Noise Mask"}}, "29": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["3", 0], "negative": ["4", 0], "control_net": ["31", 0], "image": ["34", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "30": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "31": {"inputs": {"type": "repaint", "control_net": ["30", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "32": {"inputs": {"mask": ["177", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "34": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["14", 0], "source": ["32", 0], "mask": ["26", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "37": {"inputs": {"mask1": ["160", 0], "mask2": ["181", 0]}, "class_type": "SubtractMask", "_meta": {"title": "Pixelwise(MASK - MASK)"}}, "44": {"inputs": {"seed": 1104352903870584, "steps": 20, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["92", 0], "positive": ["45", 0], "negative": ["45", 1], "latent_image": ["55", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "45": {"inputs": {"strength": 0.55, "start_percent": 0, "end_percent": 1, "positive": ["3", 0], "negative": ["4", 0], "control_net": ["31", 0], "image": ["52", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "47": {"inputs": {"mask": ["57", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Mask to Image"}}, "48": {"inputs": {"samples": ["44", 0], "vae": ["92", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "52": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["14", 0], "source": ["47", 0], "mask": ["37", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "53": {"inputs": {"samples": ["15", 0], "mask": ["200", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "Set Latent Noise Mask"}}, "55": {"inputs": {"amount": 1, "samples": ["53", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "57": {"inputs": {"mask": ["37", 0]}, "class_type": "InvertMask", "_meta": {"title": "Invert Mask"}}, "61": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["107", 0], "text_b": ["108", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "85": {"inputs": {"mask1": ["197", 0], "mask2": ["184", 0]}, "class_type": "AddMask", "_meta": {"title": "Pixelwise(MASK + MASK)"}}, "92": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "93": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["48", 0], "get_image_size": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "107": {"inputs": {"string": ""}, "class_type": "StringConstant", "_meta": {"title": "String Constant"}}, "108": {"inputs": {"string": ""}, "class_type": "StringConstant", "_meta": {"title": "String Constant"}}, "160": {"inputs": {"threshold": 100, "mask": ["85", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "173": {"inputs": {"width": 512, "height": 512, "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["5", 0], "get_image_size": ["14", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "175": {"inputs": {"library": "insightface", "provider": "CUDA"}, "class_type": "FaceAnalysisModels", "_meta": {"title": "Face Analysis Models"}}, "176": {"inputs": {"area": "face", "grow": 0, "grow_tapered": false, "blur": 13, "analysis_models": ["175", 0], "image": ["14", 0]}, "class_type": "FaceSegmentation", "_meta": {"title": "Face Segmentation"}}, "177": {"inputs": {"threshold": 100, "mask": ["176", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "180": {"inputs": {"area": "main_features", "grow": 0, "grow_tapered": false, "blur": 1, "analysis_models": ["175", 0], "image": ["14", 0]}, "class_type": "FaceSegmentation", "_meta": {"title": "Face Segmentation"}}, "181": {"inputs": {"threshold": 100, "mask": ["180", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "184": {"inputs": {"mask1": ["186", 0], "mask2": ["187", 0]}, "class_type": "AddMask", "_meta": {"title": "Pixelwise(MASK + MASK)"}}, "185": {"inputs": {"grow": ["202", 0], "blur": 5, "mask": ["188", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "186": {"inputs": {"threshold": 100, "mask": ["185", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "187": {"inputs": {"threshold": 176, "mask": ["215", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "188": {"inputs": {"background": false, "face": false, "hair": true, "glasses": false, "top-clothes": false, "bottom-clothes": false, "torso-skin": false, "image": ["173", 0], "model": ["189", 0]}, "class_type": "HumanPartsDet", "_meta": {"title": "DetHair"}}, "189": {"inputs": {"model_checkpoint": "deeplabv3p-resnet50-human.onnx", "device": "CUDA"}, "class_type": "LoadModel", "_meta": {"title": "Load DetHair Model"}}, "192": {"inputs": {"grow": ["202", 0], "blur": 5, "mask": ["195", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "193": {"inputs": {"threshold": 100, "mask": ["192", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "195": {"inputs": {"background": false, "face": false, "hair": true, "glasses": false, "top-clothes": false, "bottom-clothes": false, "torso-skin": false, "image": ["14", 0], "model": ["189", 0]}, "class_type": "HumanPartsDet", "_meta": {"title": "DetHair"}}, "197": {"inputs": {"mask1": ["193", 0], "mask2": ["211", 0]}, "class_type": "AddMask", "_meta": {"title": "Pixelwise(MASK + MASK)"}}, "200": {"inputs": {"expand": 20, "tapered_corners": true, "mask": ["37", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "202": {"inputs": {"value": 5}, "class_type": "INTConstant", "_meta": {"title": "<PERSON> Grow <PERSON>t"}}, "210": {"inputs": {"model": "CIDAS/clipseg-rd64-refined"}, "class_type": "DownloadAndLoadCLIPSeg", "_meta": {"title": "(Down)load CLIPSeg"}}, "211": {"inputs": {"text": "hair", "threshold": 0.48, "binary_mask": true, "combine_mask": false, "use_cuda": true, "blur_sigma": 0, "image_bg_level": 0.5, "invert": false, "images": ["14", 0], "opt_model": ["210", 0]}, "class_type": "BatchCLIPSeg", "_meta": {"title": "Batch CLIPSeg"}}, "215": {"inputs": {"text": "hair", "threshold": 0.48, "binary_mask": true, "combine_mask": false, "use_cuda": true, "blur_sigma": 0, "image_bg_level": 0.5, "invert": false, "images": ["173", 0], "opt_model": ["210", 0]}, "class_type": "BatchCLIPSeg", "_meta": {"title": "Batch CLIPSeg"}}, "216": {"inputs": {"filename_prefix": "aiease_hair_swap", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["93", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}