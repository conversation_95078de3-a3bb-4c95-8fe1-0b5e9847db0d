{"7": {"inputs": {"text": "", "clip": ["39", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "17": {"inputs": {"image": "https://pub-static.aiease.ai/headshotAvatar%2Fstyle_28_face_Face_woman_0005_batch_0.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "23": {"inputs": {"text": ["122", 0], "clip": ["39", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "26": {"inputs": {"guidance": 100, "conditioning": ["23", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "35": {"inputs": {"positive": ["26", 0], "negative": ["80", 0], "vae": ["39", 2], "pixels": ["44", 0]}, "class_type": "InstructPixToPixConditioning", "_meta": {"title": "InstructPixToPixConditioning"}}, "39": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "40": {"inputs": {"lora_name": "Flux/flux1-depth-dev-lora.safetensors", "strength_model": 0.5, "model": ["39", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "41": {"inputs": {"ckpt_name": "depth_anything_vitl14.pth", "resolution": 128, "image": ["17", 0]}, "class_type": "DepthAnythingPreprocessor", "_meta": {"title": "Depth Anything"}}, "44": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["66", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "59": {"inputs": {"pixels": ["60", 0], "vae": ["39", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "60": {"inputs": {"upscale_method": "nearest-exact", "megapixels": 1, "image": ["88", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "61": {"inputs": {"add_noise": "enable", "noise_seed": 185452883910559, "steps": 100, "cfg": 2, "sampler_name": "euler", "scheduler": "normal", "start_at_step": ["78", 0], "end_at_step": ["79", 0], "return_with_leftover_noise": "disable", "model": ["105", 0], "positive": ["35", 0], "negative": ["35", 1], "latent_image": ["68", 0]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> for prompt"}}, "62": {"inputs": {"samples": ["73", 0], "vae": ["39", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "65": {"inputs": {"filename_prefix": "aiease_art", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["62", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "66": {"inputs": {"blur_radius": 9, "sigma": 10, "image": ["41", 0]}, "class_type": "ImageBlur", "_meta": {"title": "Image Blur"}}, "68": {"inputs": {"amount": 4, "samples": ["59", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "73": {"inputs": {"add_noise": "enable", "noise_seed": 895863828174023, "steps": 12, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "start_at_step": ["118", 0], "end_at_step": 12, "return_with_leftover_noise": "disable", "model": ["105", 0], "positive": ["35", 0], "negative": ["35", 1], "latent_image": ["61", 0]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "Refiner KSampler"}}, "78": {"inputs": {"value": 20}, "class_type": "INTConstant", "_meta": {"title": "Similarity to image"}}, "79": {"inputs": {"value": "a+1", "a": ["78", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "80": {"inputs": {"guidance": 100, "conditioning": ["7", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "81": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.12, "start": 0, "end": 1, "max_consecutive_cache_hits": -1, "model": ["40", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "82": {"inputs": {"is_patcher": true, "object_to_patch": "diffusion_model", "compiler": "torch.compile", "fullgraph": false, "dynamic": false, "mode": "", "options": "", "disable": false, "backend": "inductor", "model": ["81", 0]}, "class_type": "EnhancedCompileModel", "_meta": {"title": "Compile Model+"}}, "88": {"inputs": {"upscale_method": "nearest-exact", "megapixels": 0.4, "image": ["17", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "105": {"inputs": {"max_shift": 1.45, "base_shift": 0, "width": 1024, "height": 1024, "model": ["82", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "112": {"inputs": {"model": "microsoft/Florence-2-large", "precision": "fp16", "attention": "sdpa"}, "class_type": "DownloadAndLoadFlorence2Model", "_meta": {"title": "DownloadAndLoadFlorence2Model"}}, "113": {"inputs": {"text_input": "", "task": "caption", "fill_mask": true, "keep_model_loaded": false, "max_new_tokens": 1024, "num_beams": 3, "do_sample": true, "output_mask_select": "", "seed": 162415567711271, "image": ["17", 0], "florence2_model": ["112", 0]}, "class_type": "Florence2Run", "_meta": {"title": "Florence2Run"}}, "118": {"inputs": {"expression": "round(6-((100-a)/100)*4)", "a": ["78", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "122": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "", "text_b": ["113", 2], "text_c": "", "result": "woman, A man in a black coat standing on a street."}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}}