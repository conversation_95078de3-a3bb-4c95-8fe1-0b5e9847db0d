{"6": {"inputs": {"noise_mask": false, "positive": ["7", 0], "negative": ["8", 0], "vae": ["26", 2], "pixels": ["36", 0], "mask": ["48", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "7": {"inputs": {"guidance": 50, "conditioning": ["24", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "8": {"inputs": {"conditioning": ["24", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "9": {"inputs": {"image": "https://pub-static.aiease.ai/aieaseExample%2FaiWardrobe%2Fk-pop%2F2-ub-f.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "10": {"inputs": {"width": ["88", 0], "height": ["89", 0], "upscale_method": "lanc<PERSON>s", "keep_proportion": "pad", "pad_color": "255, 255, 255", "crop_position": "center", "divisible_by": 2, "image": ["82", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "13": {"inputs": {"seed": 788867890553601, "steps": 12, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 0.9, "model": ["14", 0], "positive": ["6", 0], "negative": ["6", 1], "latent_image": ["6", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "14": {"inputs": {"model": ["67", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "15": {"inputs": {"samples": ["13", 0], "vae": ["26", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "19": {"inputs": {"weight": 0.6, "start_at": 0, "end_at": 1, "fusion": "mean", "fusion_weight_max": 1, "fusion_weight_min": 0, "train_step": 1000, "use_gray": true, "model": ["26", 0], "pulid_flux": ["20", 0], "eva_clip": ["21", 0], "face_analysis": ["22", 0], "image": ["10", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "20": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "21": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "22": {"inputs": {"provider": "CUDA"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "24": {"inputs": {"text": ["77", 0], "clip": ["26", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "26": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "28": {"inputs": {"text": ["76", 0], "clip": ["26", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "29": {"inputs": {"guidance": 50, "conditioning": ["28", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "30": {"inputs": {"seed": 28675726643535, "steps": 6, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["19", 0], "positive": ["29", 0], "negative": ["31", 0], "latent_image": ["32", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "31": {"inputs": {"conditioning": ["28", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "32": {"inputs": {"width": ["88", 0], "height": ["89", 0], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "33": {"inputs": {"samples": ["30", 0], "vae": ["26", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "36": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["10", 0], "image2": ["33", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "43": {"inputs": {"panel_width": ["10", 1], "panel_height": ["10", 2], "fill_color": "custom", "fill_color_hex": "#000000"}, "class_type": "CR Color Panel", "_meta": {"title": "🌁 CR Color Panel"}}, "44": {"inputs": {"mask": ["57", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "45": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["43", 0], "image2": ["44", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "48": {"inputs": {"channel": "red", "image": ["45", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "56": {"inputs": {"threshold": 10, "mask": ["59", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "57": {"inputs": {"kernel_size": 3, "sigma": 5, "mask": ["56", 0]}, "class_type": "ImpactGaussianBlurMask", "_meta": {"title": "Gaussian Blur Mask"}}, "58": {"inputs": {"model": "CIDAS/clipseg-rd64-refined"}, "class_type": "DownloadAndLoadCLIPSeg", "_meta": {"title": "(Down)load CLIPSeg"}}, "59": {"inputs": {"text": "head", "threshold": 0.25, "binary_mask": true, "combine_mask": false, "use_cuda": true, "blur_sigma": 3, "image_bg_level": 0.5, "invert": false, "images": ["33", 0], "opt_model": ["58", 0]}, "class_type": "BatchCLIPSeg", "_meta": {"title": "Batch CLIPSeg"}}, "66": {"inputs": {"model_path": "svdq-int4-flux.1-fill-dev", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "enable", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "67": {"inputs": {"lora_name": "comfyui_portrait_lora64.safetensors", "lora_strength": 1, "model": ["66", 0]}, "class_type": "NunchakuFluxLoraLoader", "_meta": {"title": "Nunchaku FLUX.1 LoRA Loader"}}, "76": {"inputs": {"string": "a high-quality professional portrait of a man, bald, close up, blue suit, white background, upper body, looking at viewer, Super - Resolution, Megapixel, f/2.8 aperture, confident and composed expression, softly lit background. realistic, front view, facing camera directly, symmetrical pose, centered composition, straight posture,looking directly at the viewer,perfectly aligned shoulders", "strip_newlines": true}, "class_type": "StringConstantMultiline", "_meta": {"title": "String Constant Multiline"}}, "77": {"inputs": {"action": "append", "tidy_tags": "no", "text_a": "Maintain the face,", "text_b": ["76", 0], "text_c": ""}, "class_type": "StringFunction|pysssss", "_meta": {"title": "Add instruction (String Function )"}}, "79": {"inputs": {"filename_prefix": "aiease_headshot_pro", "nsfw_detect": false, "file_type": "PNG", "images": ["97", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "81": {"inputs": {"library": "insightface", "provider": "CUDA"}, "class_type": "FaceAnalysisModels", "_meta": {"title": "Face Analysis Models"}}, "82": {"inputs": {"padding": 0, "padding_percent": 0.7000000000000001, "index": 0, "analysis_models": ["81", 0], "image": ["9", 0]}, "class_type": "FaceBoundingBox", "_meta": {"title": "Face Bounding Box"}}, "88": {"inputs": {"value": 1024}, "class_type": "INTConstant", "_meta": {"title": "INT Constant width"}}, "89": {"inputs": {"value": 1024}, "class_type": "INTConstant", "_meta": {"title": "INT Constant height"}}, "91": {"inputs": {"samples": ["95", 0], "vae": ["26", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "92": {"inputs": {"expand": -12, "tapered_corners": true, "mask": ["48", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "93": {"inputs": {"masks_a": ["94", 0], "masks_b": ["92", 0]}, "class_type": "Masks Subtract", "_meta": {"title": "Masks Subtract"}}, "94": {"inputs": {"expand": 12, "tapered_corners": true, "mask": ["48", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "95": {"inputs": {"add_noise": "disable", "noise_seed": 842806698386521, "steps": 15, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "start_at_step": 13, "end_at_step": 15, "return_with_leftover_noise": "disable", "model": ["66", 0], "positive": ["96", 0], "negative": ["96", 1], "latent_image": ["96", 2]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Advanced)"}}, "96": {"inputs": {"noise_mask": false, "positive": ["7", 0], "negative": ["8", 0], "vae": ["26", 2], "pixels": ["15", 0], "mask": ["93", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "97": {"inputs": {"images": ["91", 0]}, "class_type": "SplitRightImage", "_meta": {"title": "SplitRightImage"}}}