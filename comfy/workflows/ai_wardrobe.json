{"3": {"inputs": {"seed": 228583866950219, "steps": 20, "cfg": 1, "sampler_name": "dpmpp_2m", "scheduler": "sgm_uniform", "denoise": 1, "model": ["31", 0], "positive": ["38", 0], "negative": ["38", 1], "latent_image": ["38", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "7": {"inputs": {"text": "", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["32", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "17": {"inputs": {"image": "https://pub-static.aiease.ai/aieaseExample%2FaiWardrobe%2Fk-pop%2F2-ub-f.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "23": {"inputs": {"text": "This is a pair of images，the person in the right side wears the clothes on the person in the left side. Any other is not changed. ", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "26": {"inputs": {"guidance": 30, "conditioning": ["23", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "31": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "32": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "34": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "38": {"inputs": {"noise_mask": true, "positive": ["290", 0], "negative": ["7", 0], "vae": ["32", 0], "pixels": ["115", 1], "mask": ["115", 2]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "52": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "54": {"inputs": {"image": "https://pub-static.aiease.ai/aieaseExample%2FaiWardrobe%2Fk-pop%2F2-ub-f.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "56": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "58": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["605", 0], "image2": ["606", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "97": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["115", 0], "inpainted_image": ["8", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"}}, "115": {"inputs": {"context_expand_pixels": 2, "context_expand_factor": 1, "fill_mask_holes": false, "blur_mask_pixels": 0, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "forced size", "force_width": 1080, "force_height": 960, "rescale_factor": 1, "min_width": 512, "min_height": 512, "max_width": 1024, "max_height": 1024, "padding": 32, "image": ["58", 0], "mask": ["136", 0], "optional_context_mask": ["135", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "120": {"inputs": {"mask": ["485", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "121": {"inputs": {"mask": ["613", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "130": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["120", 0], "image2": ["149", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "134": {"inputs": {"direction": "left", "match_image_size": true, "image1": ["121", 0], "image2": ["152", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "135": {"inputs": {"channel": "red", "image": ["130", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "136": {"inputs": {"channel": "red", "image": ["134", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "149": {"inputs": {"width": ["606", 1], "height": ["606", 2], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "152": {"inputs": {"width": ["605", 1], "height": ["605", 2], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "167": {"inputs": {"images": ["168", 0]}, "class_type": "easy imagesSplitImage", "_meta": {"title": "imagesSplitImage"}}, "168": {"inputs": {"row": 1, "column": 2, "images": ["97", 0]}, "class_type": "easy imageSplitGrid", "_meta": {"title": "imageSplitGrid"}}, "221": {"inputs": {"invert_mask": false, "detect": "mask_area", "top_reserve": 20, "bottom_reserve": 20, "left_reserve": 20, "right_reserve": 20, "round_to_multiple": "8", "image": ["605", 0], "mask": ["485", 0]}, "class_type": "LayerUtility: CropByMask V2", "_meta": {"title": "LayerUtility: CropByMask V2"}}, "263": {"inputs": {"masks_a": ["275", 0], "masks_b": ["412", 0]}, "class_type": "Masks Subtract", "_meta": {"title": "Masks Subtract"}}, "273": {"inputs": {"expand": -2, "incremental_expandrate": 1, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["408", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "275": {"inputs": {"expand": 30, "incremental_expandrate": 1, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["373", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "279": {"inputs": {"value": 0}, "class_type": "Int Input [Dream]", "_meta": {"title": "0分割服装图1不分割服装"}}, "290": {"inputs": {"downsampling_factor": 2, "downsampling_function": "area", "mode": "keep aspect ratio", "weight": 1, "autocrop_margin": 0, "conditioning": ["26", 0], "style_model": ["56", 0], "clip_vision": ["52", 0], "image": ["601", 0], "mask": ["603", 0]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "303": {"inputs": {"width": ["605", 1], "height": ["605", 2], "batch_size": 1, "color": 255}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "304": {"inputs": {"channel": "blue", "image": ["303", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "371": {"inputs": {"threshold": 0.4, "dilation": 10, "crop_factor": 3, "drop_size": 10, "labels": "all", "segm_detector": ["372", 1], "image": ["606", 0]}, "class_type": "SegmDetectorSEGS", "_meta": {"title": "SEGM Detector (SEGS)"}}, "372": {"inputs": {"model_name": "segm/person_yolov8m-seg.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "373": {"inputs": {"segs": ["371", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "408": {"inputs": {"background": false, "head": true, "torso": false, "upper_arms": false, "lower_arms": false, "upper_legs": false, "lower_legs": false, "image": ["606", 0]}, "class_type": "<PERSON>zy <PERSON> Parser <PERSON>", "_meta": {"title": "<PERSON>zy <PERSON> Parser <PERSON>"}}, "412": {"inputs": {"masks_a": ["273", 0], "masks_b": ["535", 1]}, "class_type": "Masks Subtract", "_meta": {"title": "Masks Subtract"}}, "484": {"inputs": {"background": false, "head": false, "torso": true, "upper_arms": false, "lower_arms": false, "upper_legs": false, "lower_legs": false, "image": ["605", 0]}, "class_type": "<PERSON>zy <PERSON> Parser <PERSON>", "_meta": {"title": "<PERSON>zy <PERSON> Parser <PERSON>"}}, "485": {"inputs": {"masks_a": ["536", 1], "masks_b": ["484", 0]}, "class_type": "Masks Add", "_meta": {"title": "Masks Add"}}, "535": {"inputs": {"face": false, "hair": false, "hat": false, "sunglass": false, "left_arm": false, "right_arm": false, "left_leg": false, "right_leg": false, "upper_clothes": true, "skirt": true, "pants": true, "dress": true, "belt": true, "shoe": false, "bag": false, "scarf": false, "detail_method": "VITMatte", "detail_erode": 12, "detail_dilate": 6, "black_point": 0.1, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["606", 0]}, "class_type": "LayerMask: SegformerB2ClothesUltra", "_meta": {"title": "LayerMask: Segformer B2 Clothes Ultra"}}, "536": {"inputs": {"face": false, "hair": false, "hat": false, "sunglass": false, "left_arm": false, "right_arm": false, "left_leg": false, "right_leg": false, "upper_clothes": true, "skirt": true, "pants": true, "dress": true, "belt": true, "shoe": false, "bag": false, "scarf": false, "detail_method": "VITMatte", "detail_erode": 12, "detail_dilate": 6, "black_point": 0.1, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["605", 0]}, "class_type": "LayerMask: SegformerB2ClothesUltra", "_meta": {"title": "LayerMask: Segformer B2 Clothes Ultra"}}, "558": {"inputs": {"face": false, "hair": false, "hat": false, "sunglass": false, "left_arm": false, "right_arm": false, "left_leg": false, "right_leg": false, "upper_clothes": true, "skirt": false, "pants": false, "dress": true, "belt": false, "shoe": false, "bag": false, "scarf": false, "detail_method": "VITMatte", "detail_erode": 12, "detail_dilate": 6, "black_point": 0.1, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["606", 0]}, "class_type": "LayerMask: SegformerB2ClothesUltra", "_meta": {"title": "LayerMask: Segformer B2 Clothes Ultra"}}, "559": {"inputs": {"background": false, "head": false, "torso": true, "upper_arms": true, "lower_arms": true, "upper_legs": false, "lower_legs": false, "image": ["606", 0]}, "class_type": "<PERSON>zy <PERSON> Parser <PERSON>", "_meta": {"title": "<PERSON>zy <PERSON> Parser <PERSON>"}}, "560": {"inputs": {"masks_a": ["559", 0], "masks_b": ["558", 1]}, "class_type": "Masks Add", "_meta": {"title": "Masks Add"}}, "561": {"inputs": {"expand": 10, "incremental_expandrate": 1, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["562", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "562": {"inputs": {"threshold": 1, "mask": ["560", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "566": {"inputs": {"face": false, "hair": false, "hat": false, "sunglass": false, "left_arm": false, "right_arm": false, "left_leg": true, "right_leg": true, "upper_clothes": false, "skirt": true, "pants": true, "dress": true, "belt": false, "shoe": false, "bag": false, "scarf": false, "detail_method": "VITMatte", "detail_erode": 12, "detail_dilate": 6, "black_point": 0.1, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["606", 0]}, "class_type": "LayerMask: SegformerB2ClothesUltra", "_meta": {"title": "LayerMask: Segformer B2 Clothes Ultra"}}, "567": {"inputs": {"background": false, "head": false, "torso": false, "upper_arms": false, "lower_arms": false, "upper_legs": true, "lower_legs": true, "image": ["606", 0]}, "class_type": "<PERSON>zy <PERSON> Parser <PERSON>", "_meta": {"title": "<PERSON>zy <PERSON> Parser <PERSON>"}}, "568": {"inputs": {"masks_a": ["567", 0], "masks_b": ["566", 1]}, "class_type": "Masks Add", "_meta": {"title": "Masks Add"}}, "569": {"inputs": {"threshold": 1, "mask": ["568", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "570": {"inputs": {"expand": 10, "incremental_expandrate": 1, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["569", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "578": {"inputs": {"text": "hand", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "579": {"inputs": {"text": "", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "580": {"inputs": {"guidance": 30, "conditioning": ["578", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "581": {"inputs": {"model": ["31", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "582": {"inputs": {"noise_mask": true, "positive": ["580", 0], "negative": ["579", 0], "vae": ["32", 0], "pixels": ["608", 0], "mask": ["595", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "584": {"inputs": {"seed": 957155078927913, "steps": 10, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["31", 0], "positive": ["582", 0], "negative": ["582", 1], "latent_image": ["582", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "585": {"inputs": {"samples": ["584", 0], "vae": ["32", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "587": {"inputs": {"prompt": "hands", "threshold": 0.3, "sam_model": ["590", 0], "grounding_dino_model": ["596", 0], "image": ["608", 0]}, "class_type": "GroundingDinoSAMSegment (segment anything)", "_meta": {"title": "GroundingDinoSAMSegment (segment anything)"}}, "590": {"inputs": {"model_name": "sam_vit_h (2.56GB)"}, "class_type": "SAMModelLoader (segment anything)", "_meta": {"title": "SAMModelLoader (segment anything)"}}, "592": {"inputs": {"background": false, "head": true, "torso": true, "upper_arms": true, "lower_arms": false, "upper_legs": true, "lower_legs": true, "image": ["608", 0]}, "class_type": "<PERSON>zy <PERSON> Parser <PERSON>", "_meta": {"title": "<PERSON>zy <PERSON> Parser <PERSON>"}}, "593": {"inputs": {"masks_a": ["587", 1], "masks_b": ["592", 0]}, "class_type": "Masks Subtract", "_meta": {"title": "Masks Subtract"}}, "595": {"inputs": {"expand": 20, "incremental_expandrate": 1, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["593", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "596": {"inputs": {"model_name": "GroundingDINO_SwinT_OGC (694MB)"}, "class_type": "GroundingDinoModelLoader (segment anything)", "_meta": {"title": "GroundingDinoModelLoader (segment anything)"}}, "598": {"inputs": {"filename_prefix": "aiease_wardrobe", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["585", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "599": {"inputs": {"value": 0}, "class_type": "Int Input [Dream]", "_meta": {"title": "✍ Int Input"}}, "601": {"inputs": {"index": ["279", 0], "value0": ["221", 0], "value1": ["605", 0]}, "class_type": "easy anythingIndexSwitch", "_meta": {"title": "Any Index Switch"}}, "603": {"inputs": {"index": ["279", 0], "value0": ["221", 1], "value1": ["304", 0]}, "class_type": "easy anythingIndexSwitch", "_meta": {"title": "Any Index Switch"}}, "604": {"inputs": {"index": ["599", 0], "value0": ["629", 0], "value1": ["561", 0], "value2": ["570", 0]}, "class_type": "easy anythingIndexSwitch", "_meta": {"title": "Any Index Switch"}}, "605": {"inputs": {"width": 900, "height": 1600, "upscale_method": "lanc<PERSON>s", "keep_proportion": "pad", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 2, "image": ["54", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "606": {"inputs": {"width": 900, "height": 1600, "upscale_method": "lanc<PERSON>s", "keep_proportion": "pad", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 2, "image": ["17", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "608": {"inputs": {"width": ["612", 1], "height": ["612", 2], "upscale_method": "lanc<PERSON>s", "keep_proportion": "crop", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "image": ["167", 1]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "612": {"inputs": {"width": 900, "height": 1600, "upscale_method": "lanc<PERSON>s", "keep_proportion": "resize", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "image": ["17", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "613": {"inputs": {"mask": ["604", 0]}, "class_type": "IsMaskEmptyNode", "_meta": {"title": "IsMaskEmpty-AIEase"}}, "624": {"inputs": {}, "class_type": "MASK_PROCESS_MODEL", "_meta": {"title": "leffa mask model load"}}, "625": {"inputs": {"vt_garment_type": "dresses", "control_type": "virtual_tryon", "vt_model_type": "viton_hd", "pipe": ["624", 0], "image": ["606", 0]}, "class_type": "MASK_PROCESS", "_meta": {"title": "leffa mask process"}}, "628": {"inputs": {"masks_a": ["263", 0], "masks_b": ["640", 0]}, "class_type": "Masks Add", "_meta": {"title": "Masks Add"}}, "629": {"inputs": {"threshold": 1, "mask": ["628", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "640": {"inputs": {"expand": 10, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["625", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}}