{"1": {"inputs": {"birefnet_model": "BiRefNet-portrait"}, "class_type": "BiRefNet_ModelLoader_Ease", "_meta": {"title": "🧹 Ease BiRefNet Model Loader"}}, "2": {"inputs": {"birefnetmodel": ["1", 0], "images": ["27", 0]}, "class_type": "BiRefNet_Ease", "_meta": {"title": "🧹 Ease BiRefNet"}}, "3": {"inputs": {"image": "input.png", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "18": {"inputs": {"background": false, "face": true, "hair": false, "glasses": false, "top-clothes": false, "bottom-clothes": false, "torso-skin": false, "image": ["27", 0], "model": ["20", 0]}, "class_type": "HumanPartsDet", "_meta": {"title": "DetHair"}}, "19": {"inputs": {"background": false, "face": false, "hair": true, "glasses": false, "top-clothes": false, "bottom-clothes": false, "torso-skin": false, "image": ["27", 0], "model": ["20", 0]}, "class_type": "HumanPartsDet", "_meta": {"title": "DetHair"}}, "20": {"inputs": {"model_checkpoint": "deeplabv3p-resnet50-human.onnx", "device": "CUDA"}, "class_type": "LoadModel", "_meta": {"title": "Load DetHair Model"}}, "21": {"inputs": {"facemask": ["18", 0], "hairmask": ["19", 0]}, "class_type": "Head<PERSON>ey", "_meta": {"title": "Head<PERSON>ey"}}, "27": {"inputs": {"padding": 0, "padding_percent": 1, "index": -1, "analysis_models": ["28", 0], "image": ["3", 0]}, "class_type": "FaceBoundingBox", "_meta": {"title": "Face Bounding Box"}}, "28": {"inputs": {"library": "insightface", "provider": "CUDA"}, "class_type": "FaceAnalysisModels", "_meta": {"title": "Face Analysis Models"}}, "35": {"inputs": {"filename_prefix": "aiease_passport", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["2", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "37": {"inputs": {"text": ["21", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}}