{"6": {"inputs": {"text": "", "clip": ["56", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["59", 0], "vae": ["56", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "56": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "59": {"inputs": {"seed": 199012268695425, "steps": 6, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["56", 0], "positive": ["6", 0], "negative": ["6", 0], "latent_image": ["72", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "72": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "76": {"inputs": {"filename_prefix": "aiease_art_v1", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["8", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}