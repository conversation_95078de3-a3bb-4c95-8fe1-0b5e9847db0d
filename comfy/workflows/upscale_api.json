{"10": {"inputs": {"image": "xxx.png", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "11": {"inputs": {"upscale_model": ["13", 0], "image": ["10", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "13": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "16": {"inputs": {"upscale_method": "nearest-exact", "scale_by": 0.5, "image": ["11", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "18": {"inputs": {"filename_prefix": "aiease_upscale", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["16", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}