{"2": {"inputs": {"image": "https://pub-static.aiease.ai/headshotAvatar%2Fstyle_23_face_Face_woman_0005_batch_0.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "3": {"inputs": {"samples": ["8", 0], "vae": ["66", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "4": {"inputs": {"width": 1152, "height": 1536, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "8": {"inputs": {"seed": 230921611736898, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["39", 0], "positive": ["33", 0], "negative": ["12", 0], "latent_image": ["4", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "11": {"inputs": {"text": ["81", 0], "clip": ["65", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "12": {"inputs": {"text": "", "clip": ["65", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "18": {"inputs": {"lora_name": "F.1-男孩摄影-V1.0.safetensors", "strength_model": ["109", 0], "strength_clip": ["109", 0], "model": ["23", 0], "clip": ["23", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "19": {"inputs": {"lora_name": "F.1-欧洲小女孩-V1.0.safetensors", "strength_model": ["110", 0], "strength_clip": ["110", 0], "model": ["18", 0], "clip": ["18", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "23": {"inputs": {"lora_name": "FLUX.1-Turbo-Alpha.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["64", 0], "clip": ["65", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "33": {"inputs": {"guidance": 3.5, "conditioning": ["11", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "36": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "37": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "38": {"inputs": {"provider": "CPU"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "39": {"inputs": {"weight": ["87", 0], "start_at": 0, "end_at": ["113", 0], "fusion": "mean", "fusion_weight_max": 1, "fusion_weight_min": 0, "train_step": 1000, "use_gray": true, "model": ["73", 0], "pulid_flux": ["36", 0], "eva_clip": ["37", 0], "face_analysis": ["38", 0], "image": ["49", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "45": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "BBoxDetector<PERSON>oader(FaceParsing)", "_meta": {"title": "BBoxDetector<PERSON>oader(FaceParsing)"}}, "46": {"inputs": {"threshold": 0.3, "dilation": 8, "dilation_ratio": 0.2, "by_ratio": false, "bbox_detector": ["45", 0], "image": ["91", 0]}, "class_type": "BBoxDetect(FaceParsing)", "_meta": {"title": "BBoxDetect(FaceParsing)"}}, "47": {"inputs": {"index": 0, "bbox_list": ["46", 0]}, "class_type": "BBoxListItemSelect(FaceParsing)", "_meta": {"title": "BBoxListItemSelect(FaceParsing)"}}, "49": {"inputs": {"bbox": ["47", 0], "image": ["91", 0]}, "class_type": "ImageCropWithBBox(FaceParsing)", "_meta": {"title": "ImageCropWithBBox(FaceParsing)"}}, "64": {"inputs": {"unet_name": "flux1_v30PAPFp8.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "65": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "66": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "73": {"inputs": {"lora_name": "age_v1.safetensors", "strength_model": ["111", 0], "strength_clip": ["111", 0], "model": ["19", 0], "clip": ["19", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "80": {"inputs": {"text": "Just tell me the race and gender, of the person without other messages no more than five words, such as \"An Asian female\" or \"An African male\"", "model": "MiniCPM-V-2_6-int4", "keep_model_loaded": false, "top_p": 0.8, "top_k": 100, "temperature": 0.7, "repetition_penalty": 1.05, "max_new_tokens": 2048, "video_max_num_frames": 64, "video_max_slice_nums": 2, "seed": 947, "source_image_path_1st": ["91", 0]}, "class_type": "MiniCPM_VQA", "_meta": {"title": "MiniCPM VQA"}}, "81": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["80", 0], "text_b": ["107", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "87": {"inputs": {"style_index": ["117", 0], "age": ["106", 0], "ID_weight": ["112", 0]}, "class_type": "Calculate_pulid_weight", "_meta": {"title": "Calculate_pulid_weight"}}, "91": {"inputs": {"max_width": 1024, "max_height": 1024, "min_width": 0, "min_height": 0, "crop_if_required": "no", "images": ["2", 0]}, "class_type": "ConstrainImage|pysssss", "_meta": {"title": "Constrain Image 🐍"}}, "96": {"inputs": {"filename_prefix": "aiease_age", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["3", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "106": {"inputs": {"value": -1}, "class_type": "IntInput_aiease", "_meta": {"title": "Int Input aiease"}}, "107": {"inputs": {"value": "(portrait:1.3), (smile:1.4), outdoor half-length portrait of a 90-year-old man with deeply etched wrinkles, sagging skin, pronounced jowls, age spots, sunken cheeks, drooping eyelids, thinning hair, and a gentle smile. His skin appears rough, coarse, and weathered with prominent texture and creases. He has **white, silver-gray, thinning hair that is wispy and sparse**. He wears a sports outfit, such as a golf shirt or polo. The background features a lush golf course with green fairways and trees, under a clear blue sky."}, "class_type": "Text Input [Dream]", "_meta": {"title": "✍ Text Input"}}, "109": {"inputs": {"value": 0}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "110": {"inputs": {"value": 0}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "111": {"inputs": {"value": 2.0000000000000004}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "112": {"inputs": {"value": 0.6500000000000001}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "113": {"inputs": {"value": 0.8000000000000002}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "117": {"inputs": {"value": 16}, "class_type": "IntInput_aiease", "_meta": {"title": "Int Input aiease"}}}