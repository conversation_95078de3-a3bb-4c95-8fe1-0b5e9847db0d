{"11": {"inputs": {"enabled": false, "codeformer_fidelity": 0.5, "upscale_model": "x2", "upscale": 2, "image": ["15", 0]}, "class_type": "RestoreCFWithModel", "_meta": {"title": "RestoreCFWithModel"}}, "15": {"inputs": {"enabled": false, "image": ["16", 0]}, "class_type": "DDColorNode", "_meta": {"title": "colorization"}}, "16": {"inputs": {"image": "xx.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "19": {"inputs": {"filename_prefix": "aiease_restore", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["11", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}