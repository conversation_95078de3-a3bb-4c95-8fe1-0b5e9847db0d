{"3": {"inputs": {"category": "Dresses", "offset_top": 0, "offset_bottom": 0, "offset_left": 0, "offset_right": 0, "model": ["16", 0], "vton_image": ["29", 0]}, "class_type": "FitDiTMaskGenerator", "_meta": {"title": "Generate FitDiT Mask"}}, "4": {"inputs": {"n_steps": 20, "image_scale": 2, "seed": 1920, "num_images": 1, "resolution": "768x1024", "model": ["16", 0], "vton_image": ["35", 0], "garm_image": ["30", 0], "mask": ["3", 1], "pose_image": ["3", 2]}, "class_type": "FitDiTTryOn", "_meta": {"title": "FitDiT Try-On"}}, "16": {"inputs": {"device": "cuda", "with_fp16": true, "with_offload": false, "with_aggressive_offload": false}, "class_type": "FitDiTLoader", "_meta": {"title": "Load FitDiT Model"}}, "28": {"inputs": {"image": ["35", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "29": {"inputs": {"width": 512, "height": 512, "upscale_method": "bilinear", "keep_proportion": false, "divisible_by": 2, "width_input": ["28", 0], "height_input": ["28", 1], "crop": "disabled", "image": ["35", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "30": {"inputs": {"width": 512, "height": 512, "upscale_method": "bilinear", "keep_proportion": false, "divisible_by": 2, "width_input": ["29", 1], "height_input": ["29", 2], "crop": "disabled", "image": ["34", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "32": {"inputs": {"width": 512, "height": 512, "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["4", 0], "get_image_size": ["35", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "34": {"inputs": {"image": "https://testauth-1324066212.cos.na-siliconvalley.myqcloud.com/test/828901.png"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "AIEaseLoadImage"}}, "35": {"inputs": {"image": "https://testauth-1324066212.cos.na-siliconvalley.myqcloud.com/test/912FD42A-CC5D-45f5-BBC8-FA192BBAC344.png"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "AIEaseLoadImage"}}, "36": {"inputs": {"filename_prefix": "aiease_fitdit_try_on", "nsfw_detect": false, "file_type": "WEBP (lossless)", "images": ["32", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}