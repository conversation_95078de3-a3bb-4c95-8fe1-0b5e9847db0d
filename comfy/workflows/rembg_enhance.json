{"28": {"inputs": {"enabled": true, "codeformer_fidelity": 0.9, "upscale_model": "x4", "upscale": 4, "image": ["64", 0]}, "class_type": "RestoreCFWithModel", "_meta": {"title": "RestoreCFWithModel"}}, "64": {"inputs": {"image": "origin.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "69": {"inputs": {"mask": ["64", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "76": {"inputs": {"upscale_method": "bilinear", "scale_by": 4, "image": ["69", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "78": {"inputs": {"channel": "red", "image": ["76", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "79": {"inputs": {"image": ["28", 0], "alpha": ["78", 0]}, "class_type": "JoinImageWithAlpha", "_meta": {"title": "Join Image with Alpha"}}, "80": {"inputs": {"filename_prefix": "aiease_rembg_upscale", "nsfw_detect": false, "file_type": "WEBP (lossless)", "images": ["79", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}