{"127": {"inputs": {"seed": 168814190127792, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["310", 0], "positive": ["277", 0], "negative": ["277", 1], "latent_image": ["289", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "128": {"inputs": {"text": "", "clip": ["310", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "129": {"inputs": {"text": "NSFW,low quality,bad anatomy,sketches,grayscale,monochrome,bad proportions,worstquality", "clip": ["310", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "130": {"inputs": {"samples": ["127", 0], "vae": ["310", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "131": {"inputs": {"image": "462560055_1626040118343983_2730408537193759312_n.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "133": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "134": {"inputs": {"type": "repaint", "control_net": ["133", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "140": {"inputs": {"mask": ["194", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "142": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["315", 0], "source": ["140", 0], "mask": ["186", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "150": {"inputs": {"grow_mask_by": 6, "pixels": ["315", 0], "vae": ["310", 2], "mask": ["186", 0]}, "class_type": "VAEEncodeForInpaint", "_meta": {"title": "VAE Encode (for Inpainting)"}}, "186": {"inputs": {"expand": 4, "tapered_corners": false, "mask": ["311", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "194": {"inputs": {"mask": ["186", 0]}, "class_type": "InvertMask", "_meta": {"title": "Invert Mask"}}, "199": {"inputs": {"expand": 2, "tapered_corners": false, "mask": ["311", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "270": {"inputs": {"strength": 0.4, "start_percent": 0, "end_percent": 0.85, "positive": ["128", 0], "negative": ["129", 0], "control_net": ["134", 0], "image": ["142", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "277": {"inputs": {"strength": 0.35, "start_percent": 0, "end_percent": 1, "positive": ["270", 0], "negative": ["270", 1], "control_net": ["278", 0], "image": ["299", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "278": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["133", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "289": {"inputs": {"amount": 1, "samples": ["150", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "290": {"inputs": {"image_from": ["315", 0], "image_to": ["130", 0], "mask": ["199", 0]}, "class_type": "ImageCompositeFromMaskBatch+", "_meta": {"title": "🔧 Image Composite From Mask Batch"}}, "299": {"inputs": {"coarse": "disable", "resolution": 512, "image": ["329", 0]}, "class_type": "LineArtPreprocessor", "_meta": {"title": "Realistic Lineart"}}, "310": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "311": {"inputs": {"width": ["317", 0], "height": ["317", 1], "keep_proportions": true, "mask": ["131", 1]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "315": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["329", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "317": {"inputs": {"image": ["315", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "329": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["131", 0], "source": ["332", 0], "mask": ["131", 1]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "332": {"inputs": {"mask": ["345", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "345": {"inputs": {"expand": 2, "tapered_corners": true, "mask": ["131", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "346": {"inputs": {"filename_prefix": "aiease_aibg", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["290", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}