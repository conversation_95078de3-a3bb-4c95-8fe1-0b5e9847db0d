{"6": {"inputs": {"text": ["65", 0], "clip": ["56", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["59", 0], "vae": ["56", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "aiease_art_v1", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["8", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "27": {"inputs": {"width": 1024, "height": 1024, "batch_size": 4}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "56": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "59": {"inputs": {"seed": 174582442742198, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["62", 0], "positive": ["6", 0], "negative": ["63", 0], "latent_image": ["27", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "62": {"inputs": {"lora_name": "labubu2000.safetensors", "strength_model": 1, "model": ["56", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "63": {"inputs": {"text": "", "clip": ["56", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "65": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "<PERSON><PERSON><PERSON>", "text_b": "jumping in sky", "text_c": ""}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}}