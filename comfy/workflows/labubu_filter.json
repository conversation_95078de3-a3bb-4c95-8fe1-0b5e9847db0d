{"7": {"inputs": {"text": "", "clip": ["39", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "17": {"inputs": {"image": "https://pub-static.aiease.ai/aieaseExample%2FaiWardrobe%2Fk-pop%2F2-ub-f.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "23": {"inputs": {"text": ["121", 0], "clip": ["39", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "26": {"inputs": {"guidance": 100, "conditioning": ["23", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "39": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "59": {"inputs": {"pixels": ["132", 0], "vae": ["39", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "62": {"inputs": {"samples": ["155", 0], "vae": ["39", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "65": {"inputs": {"filename_prefix": "aiease_wardrobe", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["62", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "68": {"inputs": {"amount": 4, "samples": ["59", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "80": {"inputs": {"guidance": 100, "conditioning": ["7", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "81": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.12, "start": 0, "end": 1, "max_consecutive_cache_hits": -1, "model": ["150", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "82": {"inputs": {"is_patcher": true, "object_to_patch": "diffusion_model", "compiler": "torch.compile", "fullgraph": false, "dynamic": false, "mode": "", "options": "", "disable": false, "backend": "inductor", "model": ["81", 0]}, "class_type": "EnhancedCompileModel", "_meta": {"title": "Compile Model+"}}, "88": {"inputs": {"upscale_method": "nearest-exact", "megapixels": 0.02, "image": ["17", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "121": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "<PERSON><PERSON><PERSON>", "text_b": ["153", 0], "text_c": ""}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "132": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["88", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "150": {"inputs": {"lora_name": "labubu2000.safetensors", "strength_model": 1.2, "model": ["39", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "153": {"inputs": {"text": "Describe the character's outfit in detail, including:\nClothing style and color (e.g., A-line dress, oversized sweater, skinny jeans)\nFabric and patterns (e.g., denim, floral print, striped)\nCharacter's body type (e.g., slim, muscular, curvy, petite)\nSkin tone (e.g., fair, tan, dark, olive)\nAny accessories (e.g., sunglasses, hat, jewelry)\nBackground", "model": "MiniCPM-V-2_6-int4", "keep_model_loaded": true, "top_p": 0.8, "top_k": 100, "temperature": 0.7, "repetition_penalty": 1.05, "max_new_tokens": 1023, "video_max_num_frames": 64, "video_max_slice_nums": 2, "seed": 757, "source_image_path_1st": ["17", 0]}, "class_type": "MiniCPM_VQA", "_meta": {"title": "MiniCPM VQA"}}, "155": {"inputs": {"seed": 956237336101170, "steps": 12, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["82", 0], "positive": ["26", 0], "negative": ["80", 0], "latent_image": ["68", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}}