{"3": {"inputs": {"seed": 878580414538651, "steps": 20, "cfg": 4.5, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["97", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["75", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": "pure background, smooth, no human, no person, no characters, no objects, no items, no watermark, no subjects, clear", "clip": ["97", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "text,watermark", "clip": ["97", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["97", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "11": {"inputs": {"image": "2.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "15": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["54", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "repaint", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "48": {"inputs": {"mask": ["50", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "50": {"inputs": {"mask": ["86", 0]}, "class_type": "InvertMask", "_meta": {"title": "Invert Mask"}}, "54": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["87", 0], "source": ["48", 0], "mask": ["86", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "63": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["11", 0], "source": ["95", 0], "mask": ["64", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "64": {"inputs": {"amount": 6, "mask": ["79", 0]}, "class_type": "MaskBlur+", "_meta": {"title": "🔧 Mask Blur"}}, "71": {"inputs": {"fill": "telea", "falloff": 10, "image": ["87", 0], "mask": ["79", 0]}, "class_type": "INPAINT_MaskedFill", "_meta": {"title": "Fill Masked Area"}}, "75": {"inputs": {"pixels": ["71", 0], "vae": ["97", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "79": {"inputs": {"grow": 6, "blur": 0, "mask": ["86", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "86": {"inputs": {"width": ["89", 0], "height": ["89", 1], "keep_proportions": false, "mask": ["103", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "87": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["89", 0], "height_input": ["89", 1], "crop": "disabled", "image": ["11", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "89": {"inputs": {"image": ["11", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "92": {"inputs": {"image": ["11", 0]}, "class_type": "ImageGenResolutionFromImage", "_meta": {"title": "Generation Resolution From Image"}}, "95": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["92", 0], "height_input": ["92", 1], "crop": "disabled", "image": ["8", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "97": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "103": {"inputs": {"image": "", "channel": "red", "upload": "image"}, "class_type": "AIEaseLoadImageMask", "_meta": {"title": "Aiease Load Image (as <PERSON>)"}}, "104": {"inputs": {"filename_prefix": "aiease", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["63", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}