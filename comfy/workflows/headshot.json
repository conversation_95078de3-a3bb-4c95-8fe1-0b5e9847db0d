{"3": {"inputs": {"seed": 895718010950305, "steps": 25, "cfg": 4.5, "sampler_name": "dpm_2", "scheduler": "karras", "denoise": 1, "model": ["33", 0], "positive": ["22", 0], "negative": ["23", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "5": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "12": {"inputs": {"image": "piclumen-1718345773869.png", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "16": {"inputs": {"pulid_file": "ip-adapter_pulid_sdxl_fp16.safetensors"}, "class_type": "PulidMode<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load PuLID Model"}}, "17": {"inputs": {"provider": "CUDA"}, "class_type": "PulidInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID)"}}, "19": {"inputs": {}, "class_type": "PulidEvaClipLoader", "_meta": {"title": "<PERSON><PERSON> (PuLID)"}}, "22": {"inputs": {"text": "james jean inspired sketch of woman, in the style of james jean, abstract figuration with loose, gestural marks, sketchbook", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "23": {"inputs": {"text": "nsfw, blurry, malformed, low quality, worst quality, artifacts, noise, text, watermark, glitch, deformed, ugly, horror, ill", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "33": {"inputs": {"method": "style", "weight": 0.7000000000000001, "start_at": 0, "end_at": 1, "model": ["4", 0], "pulid": ["16", 0], "eva_clip": ["19", 0], "face_analysis": ["17", 0], "image": ["12", 0]}, "class_type": "A<PERSON>ly<PERSON><PERSON><PERSON>", "_meta": {"title": "Apply PuLID"}}, "36": {"inputs": {"filename_prefix": "aiease_headshot", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["8", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}