{"10": {"inputs": {"image": "https://pub-static.aiease.ai/headshotAvatar%2Fstyle_23_face_Face_woman_0005_batch_0.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "11": {"inputs": {"image": "https://pub-static.aiease.ai/headshotAvatar%2Fstyle_23_face_Face_woman_0005_batch_0.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "17": {"inputs": {"seed": 658760325838825, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["135", 0], "positive": ["19", 0], "negative": ["20", 0], "latent_image": ["18", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "18": {"inputs": {"width": 896, "height": 1152, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "19": {"inputs": {"text": "Porcelain skin, portrait of a 1-year-old baby, headshot, male focus, smile, clothing", "clip": ["22", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "20": {"inputs": {"text": "eyelashes, makeup, earrings, bad teeth, twisted, watermark, hand, naked, nsfw, low quality, wrinkle", "clip": ["22", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "21": {"inputs": {"samples": ["17", 0], "vae": ["22", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "22": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "24": {"inputs": {"pulid_file": "ip-adapter_pulid_sdxl_fp16.safetensors"}, "class_type": "PulidMode<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load PuLID Model"}}, "25": {"inputs": {}, "class_type": "PulidEvaClipLoader", "_meta": {"title": "<PERSON><PERSON> (PuLID)"}}, "27": {"inputs": {"provider": "CUDA"}, "class_type": "PulidInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID)"}}, "68": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "BBoxDetector<PERSON>oader(FaceParsing)", "_meta": {"title": "BBoxDetector<PERSON>oader(FaceParsing)"}}, "69": {"inputs": {"threshold": 0.3, "dilation": 8, "dilation_ratio": 0.2, "by_ratio": false, "bbox_detector": ["68", 0], "image": ["10", 0]}, "class_type": "BBoxDetect(FaceParsing)", "_meta": {"title": "BBoxDetect(FaceParsing)"}}, "70": {"inputs": {"threshold": 0.3, "dilation": 7, "dilation_ratio": 0.2, "by_ratio": false, "bbox_detector": ["68", 0], "image": ["11", 0]}, "class_type": "BBoxDetect(FaceParsing)", "_meta": {"title": "BBoxDetect(FaceParsing)"}}, "71": {"inputs": {"index": 0, "bbox_list": ["69", 0]}, "class_type": "BBoxListItemSelect(FaceParsing)", "_meta": {"title": "BBoxListItemSelect(FaceParsing)"}}, "72": {"inputs": {"bbox": ["71", 0], "image": ["10", 0]}, "class_type": "ImageCropWithBBox(FaceParsing)", "_meta": {"title": "ImageCropWithBBox(FaceParsing)"}}, "73": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": "pad", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "image": ["72", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "74": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": "pad", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "image": ["77", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "75": {"inputs": {"image1": ["73", 0], "image2": ["74", 0]}, "class_type": "ImageConcat", "_meta": {"title": "ImageConcat_AIEASE"}}, "76": {"inputs": {"index": 0, "bbox_list": ["70", 0]}, "class_type": "BBoxListItemSelect(FaceParsing)", "_meta": {"title": "BBoxListItemSelect(FaceParsing)"}}, "77": {"inputs": {"bbox": ["76", 0], "image": ["11", 0]}, "class_type": "ImageCropWithBBox(FaceParsing)", "_meta": {"title": "ImageCropWithBBox(FaceParsing)"}}, "79": {"inputs": {"method": "style", "weight_person1": ["137", 0], "weight_person2": ["138", 0], "weight": ["139", 0], "start_at": 0, "end_at": 1, "model": ["22", 0], "pulid": ["24", 0], "eva_clip": ["25", 0], "face_analysis": ["27", 0], "image": ["75", 0]}, "class_type": "ApplyPulid_aiease", "_meta": {"title": "Apply Pulid aiease"}}, "114": {"inputs": {"filename_prefix": "aiease_aifilter", "nsfw_detect": true, "file_type": "WEBP (lossy)", "images": ["21", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "134": {"inputs": {"method": "style", "weight_person1": ["137", 0], "weight_person2": ["138", 0], "weight": ["139", 0], "start_at": 0.15000000000000002, "end_at": 0.8500000000000002, "model": ["22", 0], "pulid": ["24", 0], "eva_clip": ["25", 0], "face_analysis": ["27", 0], "image": ["75", 0]}, "class_type": "ApplyPulid_aiease", "_meta": {"title": "Apply Pulid aiease"}}, "135": {"inputs": {"index": ["140", 0], "value0": ["79", 0], "value1": ["134", 0]}, "class_type": "easy anythingIndexSwitch", "_meta": {"title": "Any Index Switch"}}, "137": {"inputs": {"value": 1.0000000000000002}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "138": {"inputs": {"value": 0}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "139": {"inputs": {"value": 0.4000000000000001}, "class_type": "FloatInput_aiease", "_meta": {"title": "Float Input aiease"}}, "140": {"inputs": {"expression": "(a>=0.8) or (b>=0.8)", "a": ["137", 0], "b": ["138", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}}