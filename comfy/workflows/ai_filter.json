{"3": {"inputs": {"seed": 611142010307422, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 0.65, "model": ["26", 0], "positive": ["28", 0], "negative": ["28", 1], "latent_image": ["64", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": ["69", 0], "clip": ["26", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "text, watermark", "clip": ["26", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["27", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "21": {"inputs": {"image": "2.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "23": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["21", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "ImageScaleToTotalPixels"}}, "24": {"inputs": {"pixels": ["23", 0], "vae": ["27", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "26": {"inputs": {"lora_name": "ps1_style_SDXL_v2.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["27", 0], "clip": ["27", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "27": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "28": {"inputs": {"strength": 0.65, "start_percent": 0, "end_percent": 0.5, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["30", 0], "image": ["39", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "29": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "30": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["29", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "39": {"inputs": {"safe": "enable", "resolution": 1024, "image": ["21", 0]}, "class_type": "HEDPreprocessor", "_meta": {"title": "HED Soft-Edge Lines"}}, "64": {"inputs": {"amount": 1, "samples": ["24", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "69": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "", "text_b": ["71", 1], "text_c": ""}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "71": {"inputs": {"task": "caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["72", 0], "image": ["21", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "72": {"inputs": {"version": "base"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "75": {"inputs": {"width": 512, "height": 512, "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["8", 0], "get_image_size": ["21", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "76": {"inputs": {"filename_prefix": "aiease_aifilter", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["75", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}