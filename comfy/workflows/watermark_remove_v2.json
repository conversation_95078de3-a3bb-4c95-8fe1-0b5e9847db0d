{"6": {"inputs": {"text": "Remove all watermarks from the image.", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["31", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "31": {"inputs": {"seed": 200265411484729, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["193", 0], "positive": ["35", 0], "negative": ["135", 0], "latent_image": ["124", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "35": {"inputs": {"guidance": 2.5, "conditioning": ["177", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "38": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "39": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "42": {"inputs": {"image": ["194", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "124": {"inputs": {"pixels": ["198", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "135": {"inputs": {"conditioning": ["6", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "177": {"inputs": {"conditioning": ["6", 0], "latent": ["124", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "192": {"inputs": {"model_path": "svdq-int4_r32-flux.1-kontext-dev.safetensors", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "193": {"inputs": {"lora_name": "FLUX.1-Turbo-Alpha.safetensors", "lora_strength": 1, "model": ["192", 0]}, "class_type": "NunchakuFluxLoraLoader", "_meta": {"title": "Nunchaku FLUX.1 LoRA Loader"}}, "194": {"inputs": {"image": "https://pub-static.aiease.ai/aieaseExample%2FwatermarkRemove%2F1.webp", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "198": {"inputs": {"width": ["203", 0], "height": ["203", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": "pad", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "image": ["194", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "200": {"inputs": {"width": ["204", 0], "height": ["204", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": "crop", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "image": ["8", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "202": {"inputs": {"filename_prefix": "aiease_watermark_remove", "nsfw_detect": false, "file_type": "WEBP (lossless)", "images": ["200", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}, "203": {"inputs": {"image": ["42", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "204": {"inputs": {"image": ["194", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}}