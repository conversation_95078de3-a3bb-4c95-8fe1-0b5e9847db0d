{"3": {"inputs": {"image": "origin.jpg", "upload": "image"}, "class_type": "AIEaseLoadImage", "_meta": {"title": "Aiease Load Image"}}, "11": {"inputs": {"erode": ["42", 0], "dilate": ["62", 0], "mask": ["63", 0]}, "class_type": "Generate Trimap (mtb)", "_meta": {"title": "Generate Trimap (mtb)"}}, "19": {"inputs": {"returns": "RGBA", "model": ["20", 0], "image": ["51", 0], "trimap": ["11", 0]}, "class_type": "Apply Vit <PERSON> (mtb)", "_meta": {"title": "Apply Vit <PERSON> (mtb)"}}, "20": {"inputs": {"kind": "Composition-1K", "autodownload": true}, "class_type": "Load Vit Matte Model (mtb)", "_meta": {"title": "Load Vit Matte Model (mtb)"}}, "29": {"inputs": {"threshold": 10, "mask": ["89", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "31": {"inputs": {"output_type": "float", "masks": ["63", 0]}, "class_type": "MaskToWeightofImage", "_meta": {"title": "GetMaskRatio"}}, "32": {"inputs": {"expression": "round((b*c*a/200)**(1/2))", "a": ["31", 0], "b": ["33", 1], "c": ["33", 2]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "33": {"inputs": {"image": ["51", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "42": {"inputs": {"expression": "round(a*2/3)", "a": ["32", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "51": {"inputs": {"upscale_method": "nearest-exact", "megapixels": 1, "image": ["3", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "62": {"inputs": {"expression": "round(a*1/2)", "a": ["42", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "63": {"inputs": {"kernel_size": 3, "sigma": 5, "mask": ["29", 0]}, "class_type": "ImpactGaussianBlurMask", "_meta": {"title": "Gaussian Blur Mask"}}, "89": {"inputs": {"mask1": ["92", 0], "mask2": ["94", 0]}, "class_type": "AddMask", "_meta": {"title": "Pixelwise(MASK + MASK)"}}, "92": {"inputs": {"text": "head", "threshold": 0.25, "binary_mask": true, "combine_mask": false, "use_cuda": true, "blur_sigma": 3, "image_bg_level": 0.5, "invert": false, "images": ["51", 0], "opt_model": ["93", 0]}, "class_type": "BatchCLIPSeg", "_meta": {"title": "Batch CLIPSeg"}}, "93": {"inputs": {"model": "CIDAS/clipseg-rd64-refined"}, "class_type": "DownloadAndLoadCLIPSeg", "_meta": {"title": "(Down)load CLIPSeg"}}, "94": {"inputs": {"text": "hair", "threshold": 0.4, "binary_mask": true, "combine_mask": false, "use_cuda": true, "blur_sigma": 0, "image_bg_level": 0.5, "invert": false, "images": ["51", 0], "opt_model": ["93", 0]}, "class_type": "BatchCLIPSeg", "_meta": {"title": "Batch CLIPSeg"}}, "100": {"inputs": {"width": ["111", 1], "height": ["111", 2], "keep_proportions": false, "mask": ["19", 1]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "105": {"inputs": {"image": ["3", 0], "alpha": ["107", 0]}, "class_type": "JoinImageWithAlpha", "_meta": {"title": "Join Image with Alpha"}}, "107": {"inputs": {"mask": ["100", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "111": {"inputs": {"image": ["3", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "113": {"inputs": {"filename_prefix": "ai_face_cutout", "nsfw_detect": false, "file_type": "WEBP (lossy)", "images": ["105", 0]}, "class_type": "AIEaseSaveImage", "_meta": {"title": "AIEaseSaveImage"}}}