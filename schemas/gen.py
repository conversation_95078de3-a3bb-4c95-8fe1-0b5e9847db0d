from pydantic import BaseModel, field_validator, model_validator, Field

from comfy.defines import SAMPLER_LIST, SCHEDULER_LIST, enhance_conf
from utils.utils import is_url, replace_global_acceleration_cos_domain


class BaseItem(BaseModel):
    priority: int | None = None  # 优先级 -1 -2 越小越先执行 用于插队
    extra: dict = {}

    class Config:
        protected_namespaces = ()

    @field_validator("priority")
    def check_priority(cls, attr: int | None) -> int | None:

        if attr is None:
            return attr
        if attr not in [-1, -2,-3,-4, None]:
            raise ValueError(f"'{attr}' is not a valid priority, -1 -2 -3 -4 0 or None")
        return attr


class Img2ImgBaseItem(BaseItem):
    img_url: str

    @field_validator("img_url")
    def check_img_url(cls, attr: str) -> str:
        img_url = replace_global_acceleration_cos_domain(attr)
        return img_url


class HeadshotItem(Img2ImgBaseItem):
    task_type: str = Field("headshot", exclude=True)
    prompt: str
    negative_prompt: str
    cfg: float
    steps: int
    sampler_name: str
    scheduler: str
    pulid_method: str
    pulid_weight: float
    batch_size: int = 1
    width: int = 832
    height: int = 1216

    @field_validator("prompt")
    def check_prompt(cls, attr: str) -> str:

        return attr

    @field_validator("sampler_name")
    def check_sampler(cls, attr: str) -> str:
        if attr not in SAMPLER_LIST:
            raise ValueError(f"'{attr}' is not a valid sampler name")
        return attr

    @field_validator("scheduler")
    def check_scheduler(cls, attr: str) -> str:
        if attr not in SCHEDULER_LIST:
            raise ValueError(f"'{attr}' is not a valid scheduler name")
        return attr

    @field_validator("steps")
    def check_steps(cls, attr: int) -> int:
        if any([
            attr > 60,
            attr < 1,
        ]):
            raise ValueError(f"'{attr}' is not a valid number of steps, 1 <= steps <= 60")
        return int(attr)

    @field_validator("cfg")
    def check_cfg(cls, attr: float) -> float:
        if any([
            attr > 30,
            attr < 1,
        ]):
            raise ValueError(f"'{attr}' is not a valid cfg value, 1 <= cfg <= 30")
        return float(attr)

    @field_validator("batch_size")
    def check_batch_size(cls, attr: int) -> int:
        if any([
            attr > 8,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid batch size, 0 < batch_size < 8")
        return int(attr)

    @field_validator("height")
    def check_height(cls, attr: int) -> int:
        if any([
            attr > 2526,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid height value, 0 < height < 2526")
        return int(attr)

    @field_validator("width")
    def check_width(cls, attr: int) -> int:
        if any([
            attr > 2526,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid width value, 0 < height < 2526")
        return int(attr)


class RembgItem(Img2ImgBaseItem):
    task_type: str = Field("rembg", exclude=True)


class BgBlurItem(Img2ImgBaseItem):
    task_type: str = Field("bg_blur", exclude=True)


class PassportItem(Img2ImgBaseItem):
    task_type: str = Field("passport", exclude=True)


class EnhanceItem(Img2ImgBaseItem):
    task_type: str = Field("enhance", exclude=True)
    size: int
    mode: str
    restore: bool

    @model_validator(mode='after')
    def check(cls, values):
        size = values.size
        mode = values.mode
        restore = values.restore
        key = f'{mode}_{size}x'
        conf = enhance_conf.get(key, {})
        if not conf:
            raise ValueError(f"{key} must be not empty")
        model = conf.get('model')
        face_restore = conf.get('face_restore')
        recolor = conf.get('recolor')
        if restore and conf.get('support_restore'):
            face_restore = True
        values.extra = {
            'model': model,
            'face_restore': face_restore,
            'recolor': recolor,
            'mode': mode,
            'size': size
        }
        return values


class AiFilterItem(Img2ImgBaseItem):
    task_type: str = Field("ai_filter", exclude=True)
    base_model: str
    lora: str
    cfg: float
    denoise: float
    strength_model: float
    strength_clip: float

    text_a: str
    text_c: str

    cn_strength: float
    cn_start: float
    cn_end: float

    negative_prompt: str
    t_type: str = "caption"
    task_token: int = 1024
    batch_size: int = 1
    workflow_type: str = "ai_filter"

class RestoreItem(Img2ImgBaseItem):
    task_type: str = Field("restore", exclude=True)
    restore: bool = False
    recolor: bool = False


class FaceCropItem(Img2ImgBaseItem):
    task_type: str = Field("face_crop", exclude=True)


class FaceSwapItem(Img2ImgBaseItem):
    task_type: str = Field("face_swap", exclude=True)
    swap_face_info: dict

    @model_validator(mode='after')
    def check(cls, values):
        index_list = []
        url_list = []
        swap_face_info = values.swap_face_info
        if not swap_face_info:
            raise ValueError(f"swap_face_info must be not empty")
        if len(swap_face_info) > 30:
            raise ValueError(f"Maximum of 30 faces allowed")
        for index, url in swap_face_info.items():
            if not url:
                continue
            try:
                index = int(index)
                index = str(index)
            except ValueError:
                raise ValueError(f"index must be an integer")
            if not is_url(url):
                raise ValueError(f"url must be a valid url")
            url = replace_global_acceleration_cos_domain(url)  # 替换成全球加速地址
            index_list.append(index)
            url_list.append(url)
        # comfyui需要 0,1,2 0,1,2 等格式
        data = {
            'input_index': ','.join(index_list),
            'source_index': ','.join([str(i) for i in range(len(index_list))]),
            'source_urls': ','.join(url_list)
        }
        values.extra = data
        return values


class ObjectRemoveItem(Img2ImgBaseItem):
    task_type: str = Field("object_remove", exclude=True)
    mask_url: str = ""
    custom: str = ""

    @field_validator("mask_url")
    def check_mask_url(cls, attr: str) -> str:
        mask_url = replace_global_acceleration_cos_domain(attr)
        return mask_url


class TextRemoveItem(Img2ImgBaseItem):
    task_type: str = Field("text_remove", exclude=True)


class AiBabyItem(Img2ImgBaseItem):
    task_type: str = Field("ai_baby", exclude=True)
    parent2: str
    prompt: str
    weight: float
    img1_weight: float = 0.5
    img2_weight: float = 0.5

    @field_validator("parent2")
    def check_parent2(cls, attr: str) -> str:
        parent2 = replace_global_acceleration_cos_domain(attr)
        return parent2


class ObjectSwapItem(Img2ImgBaseItem):
    task_type: str = Field("object_swap", exclude=True)
    mask_url: str = ""
    source: str = ""
    target: str
    batch_size: int = 2

    @field_validator("mask_url")
    def check_mask_url(cls, attr: str) -> str:
        mask_url = replace_global_acceleration_cos_domain(attr)
        return mask_url


class HairSwapItem(Img2ImgBaseItem):
    task_type: str = Field("hair_swap", exclude=True)
    style_prompt: str
    color_prompt: str


class AiBgItem(Img2ImgBaseItem):
    task_type: str = Field("ai_bg", exclude=True)
    prompt: str
    negative_prompt: str


class AiEnlargeItem(Img2ImgBaseItem):
    task_type: str = Field("ai_enlarge", exclude=True)
    top: int = 0
    bottom: int = 0
    left: int = 0
    right: int = 0

    @model_validator(mode='after')
    def check(cls, values):
        top = values.top
        bottom = values.bottom
        left = values.left
        right = values.right
        values.extra = {
            "position": {
                "top": top,
                "bottom": bottom,
                "left": left,
                'right': right
            }
        }
        return values


class AiSkinRepairItem(Img2ImgBaseItem):
    task_type: str = Field("ai_skin_repair", exclude=True)
    mask_url: str = ""

    @field_validator("mask_url")
    def check_mask_url(cls, attr: str) -> str:
        mask_url = replace_global_acceleration_cos_domain(attr)
        return mask_url


class AiRecolorItem(Img2ImgBaseItem):
    task_type: str = Field("ai_recolor", exclude=True)
    color: str = ""
    position: str = ""
    mask_url: str = ""

    @field_validator("mask_url")
    def check_mask_url(cls, attr: str) -> str:
        mask_url = replace_global_acceleration_cos_domain(attr)
        return mask_url


class AiArtV1Item(BaseItem):
    task_type: str = Field("art_v1", exclude=True)
    prompt: str = ""
    width: int
    height: int
    batch_size: int = 1
    workflow_type: str = "art_v1"
    ref_weight: float = 0.8
    ref_img: str = ""

    @field_validator("ref_img")
    def check_ref_img(cls, attr: str) -> str:
        ref_img = replace_global_acceleration_cos_domain(attr)
        return ref_img


class AiFaceCutoutItem(Img2ImgBaseItem):
    task_type: str = Field("ai_face_cutout", exclude=True)


class TryOnItem(Img2ImgBaseItem):
    task_type: str = Field("try_on", exclude=True)
    garment_type: str
    cloth_url: str = ""

    @field_validator("cloth_url")
    def check_cloth_url(cls, attr: str) -> str:
        cloth_url = replace_global_acceleration_cos_domain(attr)
        return cloth_url

    @field_validator("garment_type")
    def check_garment_type(cls, attr: str) -> str:
        garment_type_mapping = {
            "ub": "upper_body",
            "lb": "lower_body",
            "dress": "dresses"
        }
        if attr in garment_type_mapping:
            return garment_type_mapping[attr]
        else:
            raise ValueError(f"Invalid type: {attr}.")

class AiWardrobeItem(Img2ImgBaseItem):
    task_type: str = Field("ai_wardrobe", exclude=True)
    garment_type: str
    cloth_url: str = ""

    @field_validator("cloth_url")
    def check_cloth_url(cls, attr: str) -> str:
        cloth_url = replace_global_acceleration_cos_domain(attr)
        return cloth_url

    @field_validator("garment_type")
    def check_garment_type(cls, attr: str) -> str:
        garment_type_mapping = {
            "ub": 1,
            "lb": 2,
            "dress": 0
        }
        if attr in garment_type_mapping:
            return garment_type_mapping[attr]
        else:
            raise ValueError(f"Invalid type: {attr}.")

class AiFilterGhibliItem(Img2ImgBaseItem):
    task_type: str = Field("ai_filter_ghibli", exclude=True)

class HeadshotProItem(Img2ImgBaseItem):
    task_type: str = Field("headshot_pro", exclude=True)
    prompt: str = ""
class FaceDetectItem(Img2ImgBaseItem):
    task_type: str = Field("face_detect", exclude=True)
