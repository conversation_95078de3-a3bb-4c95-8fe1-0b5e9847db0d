from fastapi import FastAP<PERSON>
from tortoise.contrib.fastapi import register_tortoise

from conf.settings import MYSQL_CONNECTION

DB_ORM_CONFIG = {
    'connections': {
        'default': MYSQL_CONNECTION,
    },
    'apps': {
        'models': {
            'models': ["models.orm.base", "aerich.models"],
            'default_connection': 'default',
        }
    },
    'use_tz': False,
    'timezone': 'Asia/Shanghai'
}


async def register_mysql(app: FastAPI):
    # 注册数据库
    register_tortoise(
        app,
        config=DB_ORM_CONFIG,
        generate_schemas=False,
        add_exception_handlers=False,
    )
