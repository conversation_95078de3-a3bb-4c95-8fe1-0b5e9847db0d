from conf.settings import DEFAULT_REDIS, TASK_REDIS, MQ_REDIS
import aioredis
import redis


def get_async_redis(redis_config: dict) -> aioredis.Redis:
    """
    获取异步redis连接
    :param redis_config:
    :return:
    """
    return aioredis.from_url(
        url="redis://{}".format(redis_config['host']),
        port=redis_config['port'],
        password=redis_config['password'],
        db=redis_config['db'],
        encoding="utf-8",
        decode_responses=True
    )


def get_sync_redis(redis_config: dict) -> redis.Redis:
    """
    获取同步redis连接
    :param redis_config:
    :return:
    """
    return redis.Redis(**redis_config, decode_responses=True)


async_cache_redis = get_async_redis(DEFAULT_REDIS)
cache_redis = get_sync_redis(DEFAULT_REDIS)  # noqa

# 存储任务状态的redis
async_task_redis = get_async_redis(TASK_REDIS)
task_redis = get_sync_redis(TASK_REDIS)  # noqa

# pub/sub redis
mq_redis = get_sync_redis(MQ_REDIS)
