-- upgrade --
CREATE TABLE IF NOT EXISTS `rembg_background_template` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `sort` INT   COMMENT '排序' DEFAULT 999,
    `avatar` VARCHAR(1024)   COMMENT '图标',
    `status` BOOL NOT NULL  COMMENT '启用状态' DEFAULT 1
) CHARACTER SET utf8mb4 COMMENT='背景模版表';
-- downgrade --
DROP TABLE IF EXISTS `rembg_background_template`;
