-- upgrade --
CREATE TABLE IF NOT EXISTS `country_info` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `iso_code` VARCHAR(16) NOT NULL UNIQUE COMMENT 'iso code',
    `cn_name` VARCHAR(64)   COMMENT '中文名',
    `priority` INT NOT NULL  COMMENT '优先级' DEFAULT 0
) CHARACTER SET utf8mb4 COMMENT='全球国家或者地区信息表';
-- downgrade --
DROP TABLE IF EXISTS `country_info`;
