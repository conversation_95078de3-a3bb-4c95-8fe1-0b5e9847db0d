import json

from databases.mongo import async_db as db
from databases.redis import async_cache_redis as cache
from utils.consts import COMFY_RESOURCE_KEY


async def get_comfy_resources(**kwargs):
    use_cache = kwargs.get('use_cache', True)  # 默认使用缓存
    resource_info_list = []
    if use_cache:
        # 从缓存中拿数据
        cache_data = await cache.get(COMFY_RESOURCE_KEY)
        resource_info_list = json.loads(cache_data) if cache_data else []
    if not resource_info_list:
        resource_info_list = await db.server_resource.find({"status": 1}).to_list(None)
        [_.pop("_id", None) for _ in resource_info_list]  # pop掉bson的_id
        await cache.set(COMFY_RESOURCE_KEY, json.dumps(resource_info_list), ex=60 * 30)
    return resource_info_list


async def get_comfy_address_desc_map():
    query = {"status": 1}
    items = await db.server_resource.find(query).to_list(None)
    return {i['address']: i['desc'] for i in items}
