import bson
from bson import ObjectId

from databases.mongo import async_db as db, sync_db
from pymongo.results import InsertOneResult

from utils.time_utils import current_timer


async def create_task(info: dict) -> InsertOneResult:
    """
    创建任务
    :param info:
    :return:
    """
    return await db.task.insert_one(info)


async def get_task_info(task_id: str) -> dict:
    info = await db.task.find_one({'_id': ObjectId(task_id)})
    return info

def sync_get_task_info(task_id: str) -> dict:
    """
    同步获取任务信息
    :param task_id: 任务ID
    :return: 任务信息字典
    """
    try:
        return sync_db.task.find_one({'_id': ObjectId(task_id)})
    except Exception as e:
        return {}
# ==================== 以下是 sync 函数====================

def sync_update_task(item_id, update_info: dict):
    """
    适配task_id 或者prompt_id更新任务
    :param item_id: task_id或者prompt_id
    :param update_info:
    :return:
    """
    try:
        item_id = bson.ObjectId(item_id)
        key = '_id'
    except bson.errors.InvalidId:
        key = 'prompt_id'

    current = current_timer()
    update_info.update({
        'update_time': current
    })
    if update_info.get('status') in [2, 3]:
        # 2(任务成功) 3(任务失败)
        update_info['end_time'] = current
    sync_db.task.update_one({key: item_id}, {"$set": update_info})
