from celery import Celery
from conf.settings import WARN_BROKER_URL

app = Celery('tasks')

app.conf.update(
    task_queues={
        'default': {
            'exchange': 'default',
            'exchange_type': 'direct',
            'routing_key': 'default'
        }
    },
    timezone="Asia/Shanghai",
    broker_url=WARN_BROKER_URL,
    task_protocol=1,
    task_default_queue='default',
    task_default_exchange='default',
    task_default_routing_key='default',
    result_expires=500,
    CELERY_MAX_TASKS_PER_CHILD=100,
    task_soft_time_limit=7200,
    task_time_limit=7200,
    accept_content=['json', 'pickle'],
    worker_concurrency=500
)

if __name__ == '__main__':
    app.start()
