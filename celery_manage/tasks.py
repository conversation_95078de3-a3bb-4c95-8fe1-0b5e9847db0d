import json

import requests

from comfy.apis import ImgGeneratorToolComfy
from core.Log import error_logger, task_logger, resource_logger
from services.task import sync_update_task
from utils.caches import sync_set_comfy_status, increment_task, decrement_task, store_prompt_taskid, \
    store_prompt_comfy_server, get_prompt_comfy_server, store_task_error, get_prompt_taskid
from utils.consts import COMFY_RESOURCE_KEY
from utils.lock_utils import acquire_lock
from utils.ssh import restart_comfyui
from utils.task_utils import record_service_error, handle_success_task, task_log, get_error_msg, get_task_id, \
    publish_mq, is_server_excluded,server_error_task_count
from . import celery_app
from utils.time_utils import timer
from databases.redis import cache_redis

prompt_start_time_dict = {}

failed_task_set = set()


@celery_app.task()
def gen_task(task_id: str, params: dict, comfy_server: str):
    """
    异步调用comfyui生成任务
    :param task_id: 任务id
    :param params: 参数
    :param comfy_server: comfy地址
    :return:
    """
    error = ""
    start_time = timer()  # 开始时间
    prompt_id, prompt = "", ""

    try:
        increment_task(server=comfy_server)
        obj = ImgGeneratorToolComfy(server_address=comfy_server, **params)
        prompt_id = obj.gen(**params)
        # prompt = obj.prompt
        store_prompt_comfy_server(prompt_id=prompt_id, comfy_server=comfy_server)  # 保存prompt对应comfyui地址
        store_prompt_taskid(prompt_id=prompt_id, task_id=task_id)  # 保存prompt对应task_id
        record_service_error(server=comfy_server, success=True)  # 成功的话清空服务错误记录
    except Exception as e:
        error = f"task_id: {task_id} gen image failed, task to comfy exception: {str(e)}"
        record_service_error(server=comfy_server)  # 记录服务错误
        if is_server_excluded(server=comfy_server):
            # 到达一个的阈值就将该地址下线
            error_count = server_error_task_count(server=comfy_server)
            error_logger.log(f"达到失败阈值{error_count}自动下线f{comfy_server} offline")
            offline_resource.delay(comfy_server)
    cost_time = timer() - start_time  # 生成任务耗时时间
    update_info = {
        'create_cost_time': cost_time,
        'error': error,
        # 'prompt': prompt,  # 去掉prompt记录 数据太大
        'prompt_id': prompt_id,
    }
    if not prompt_id:  # 任务创建失败
        publish_mq(task_id=task_id, message=error, status=1, error_code=4)
        decrement_task(server=comfy_server)
        update_info['status'] = 3  # 0未进行 1进行中 2成功 3失败
        sync_update_task(item_id=task_id, update_info=update_info)
        return
    update_info['status'] = 1
    sync_update_task(item_id=task_id, update_info=update_info)  # 更新任务状态


@celery_app.task()
def process_websocket_message(message, address):
    """
    处理所有的websocket消息
    :param message:
    :param address:
    :return:
    """
    prompt_data = message.get('data', {})
    prompt_id = prompt_data.get('prompt_id', "")
    msg_type = message.get('type')
    if msg_type == 'execution_start':
        task_logger.info(f'start gen image {prompt_id}')
        start_time = timer()  # start开始时才有时间
        if prompt_id not in prompt_start_time_dict:
            prompt_start_time_dict.update({prompt_id: start_time})
    elif msg_type == 'status':
        # 排队状态的数据
        queue_info = message.get('data', {}).get('status', {}).get('queue_info', {})
        queue_running_tasks = queue_info.get('queue_running', [])
        queue_pending_tasks = queue_info.get('queue_pending', [])
        running_task_dict = {task[1]: task[0] for task in queue_running_tasks}
        pending_task_dict = {task[1]: task[0] for task in queue_pending_tasks}

        taskid_info_dict = {}  # 所有prompt_id的结果
        for prompt_id, num in running_task_dict.items():
            task_id = get_task_id(prompt_id=prompt_id)
            if not task_id:
                continue
            taskid_info_dict.update({task_id: {"prompt_status": "running", "index": 0}})
        # 按number prompt_id排序
        sort_tasks = [k for k, v in sorted(pending_task_dict.items(), key=lambda x: (x[1], x[0]))]
        for prompt_id, num in pending_task_dict.items():
            task_id = get_task_id(prompt_id=prompt_id)
            if not task_id:
                continue
            try:
                index = sort_tasks.index(prompt_id)
                index = index + 1  # 在队列里0代表第1个
            except ValueError:
                # 没在pending里面就在running或者success
                index = 0
            taskid_info_dict.update({task_id: {"prompt_status": "pending", "index": index}})
        sync_set_comfy_status(server=address, server_info=taskid_info_dict)  # 保存排队状态到redis
        if running_task_dict:
            publish_mq(status_info=taskid_info_dict)

    elif msg_type == 'executing':
        data = message['data']
        if data['node'] is None and data['prompt_id'] == prompt_id and (prompt_id not in failed_task_set):
            comfy_address = get_prompt_comfy_server(prompt_id)
            decrement_task(server=comfy_address)  # 服务器计数-1
            start_time = prompt_start_time_dict.get(prompt_id)
            handle_success_task(address, prompt_id, start_time=start_time)  # 处理成功的任务
            task_log(
                f"prompt run success, {address} {prompt_id} cost {timer() - prompt_start_time_dict.get(prompt_id)}")
    elif msg_type == 'execution_error':
        comfy_address = get_prompt_comfy_server(prompt_id)
        decrement_task(server=comfy_address)  # 服务器计数-1
        task_id = get_prompt_taskid(prompt_id)
        task_log(f"prompt run error, {address} {prompt_id} cost {timer() - prompt_start_time_dict.get(prompt_id)}")
        failed_task_set.add(prompt_id)
        error = prompt_data.get('exception_message', "")
        comfy_error, error_code = get_error_msg(prompt_data)  # 获取轮训报错的错误信息
        publish_mq(task_id=task_id, message=comfy_error, status=1, error_code=error_code)  # 推送错误信息到mq
        store_task_error(task_id=task_id, error=comfy_error)  # 报错错误信息到redis 用于轮训
        sync_update_task(item_id=prompt_id, update_info={
            'status': 3,
            'origin_urls': [],
            'thumb_urls': [],
            'error': error,
        })
        error_logger.error(f"gen image error {prompt_id}, {prompt_data}")


@celery_app.task()
def offline_resource(address):
    """
    服务器下线
    :return:
    """
    if not acquire_lock(lock_name=f"offline:lock:{address}", lock_timeout=120):
        # 防止短时间内重复下线
        return
    cache_data = cache_redis.get(COMFY_RESOURCE_KEY)
    cache_data = json.loads(cache_data) if cache_data else []
    if not cache_data:
        return
    tag, desc = "", ""
    for item in cache_data:
        if item["address"] == address:
            item["status"] = 0
            tag = item.get("tag", [])
            desc = item.get("desc", "")
    cache_redis.set(COMFY_RESOURCE_KEY, json.dumps(cache_data), 60 * 30)

    message = f"offline: {address}, {tag}, {desc}"
    resource_logger.info(message)
    try:
        log_output1, log_output2 = restart_comfyui(address)  # 重启comfyui
    except Exception as e:
        resource_logger.info(str(e))
        log_output1, log_output2 = "", ""
    try:
        url = "https://oapi.dingtalk.com/robot/send?access_token=770539d834cc67bb0a79f4b08862d94bcc2a1c7cb5f7fffaa002a87ae4007adf"
        data = {
            "msgtype": "text",
            "text": {
                "content": f"Aiease服务器下线重启\n{message}\n重启前日志:{log_output1}\n重启后日志:{log_output2}",
            }
        }
        requests.post(url, headers={'Content-Type': 'application/json'}, json=data, verify=False, timeout=5)
    except Exception as e:
        resource_logger.info(str(e))
    if "0.0.0.0" in log_output2:
        # 重启成功后删除缓存 0.0.0.0表示重启成功
        resource_logger.info("success delete cache")
        cache_redis.delete(COMFY_RESOURCE_KEY)
