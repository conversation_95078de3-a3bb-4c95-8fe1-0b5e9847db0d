import json

import requests

from core.comfy.apis import Img2ImgToolComfy, Video2VideoToolComfy, Text2ImgToolComfy
from utils import strings
from utils.consts import COMFY_RESOURCE_KEY
from utils.fmts import *
from core.Log import error_logger, task_logger, prompt_logger, resource_logger
from databases.redis import task_redis, cache_redis
from utils.caches import sync_set_comfy_status, decrement_task
from utils.lock_utils import acquire_lock
from utils.ssh import restart_comfyui
from utils.task_utils import update_task_by_id, save_img_result_to_db, handle_success_task, get_other_params, \
    get_error_msg, record_service_error, is_server_excluded, task_log

from utils.time_utils import timer

from . import celery_app

prompt_start_time_dict = {}

failed_task_set = set()


@celery_app.task()
def process_websocket_message(message, address):
    """
    处理所有的websocket消息
    :param message:
    :param address:
    :return:
    """
    prompt_data = message.get('data', {})
    prompt_id = prompt_data.get('prompt_id', "")
    prompt_id_comfy_key = prompt_comfy_address_fmt.format(prompt_id)  # 找prompt id对应comfyui地址的key 用于将队列任务-1
    msg_type = message.get('type')
    if msg_type == 'execution_start':
        task_logger.info(f'start gen image {prompt_id}')
        start_time = timer()  # start开始时才有时间
        if prompt_id not in prompt_start_time_dict:
            prompt_start_time_dict.update({prompt_id: start_time})
    elif msg_type == 'status':
        # 排队状态的数据
        queue_info = message.get('data', {}).get('status', {}).get('queue_info', {})
        queue_running_tasks = queue_info.get('queue_running', [])
        queue_pending_tasks = queue_info.get('queue_pending', [])
        running_task_dict = {task[1]: task[0] for task in queue_running_tasks}
        pending_task_dict = {task[1]: task[0] for task in queue_pending_tasks}

        prompt_info_dict = {}  # 所有prompt_id的结果
        for prompt_id, num in running_task_dict.items():
            prompt_info_dict.update({prompt_id: {"prompt_status": "running", "index": 0}})
        sort_tasks = [k for k, v in sorted(pending_task_dict.items(), key=lambda x: (x[1], x[0]))]
        for prompt_id, num in pending_task_dict.items():
            try:
                index = sort_tasks.index(prompt_id)
                index = index + 1  # 在队列里0代表第1个
            except ValueError:
                # 没在pending里面就在running或者success
                index = 0
            prompt_info_dict.update({prompt_id: {"prompt_status": "pending", "index": index}})
        sync_set_comfy_status(server=address, server_info=prompt_info_dict)
        task_logger.info(f'queue status: {prompt_info_dict}')

    elif msg_type == 'executing':
        data = message['data']
        if data['node'] is None and data['prompt_id'] == prompt_id and (prompt_id not in failed_task_set):
            handle_success_task(address, prompt_id)
            comfy_address = task_redis.get(prompt_id_comfy_key)
            decrement_task(server=comfy_address)
            task_log(
                f"prompt run success, {address} {prompt_id} cost {timer() - prompt_start_time_dict.get(prompt_id)}")
    elif msg_type == 'execution_error':
        task_log(f"prompt run error, {address} {prompt_id} cost {timer() - prompt_start_time_dict.get(prompt_id)}")
        failed_task_set.add(prompt_id)
        error = prompt_data.get('exception_message', "")
        comfy_error = get_error_msg(prompt_data)  # 获取轮训报错的错误信息
        task_redis.set(PROMPT_ID_ERROR_FMT.format(prompt_id), comfy_error, ex=60 * 60)  # noqa
        # 除非是comfy默认错误，否则认为是正确的任务, 这种任务是用户上传的图片有问题 比如没人脸 没有mask
        if comfy_error != strings.COMFY_DEFAULT_ERROR:
            task_state = 5
        else:
            task_state = None
        save_img_result_to_db(prompt_id=prompt_id, img_urls=[], thumbnail_urls=[], error=error, task_state=task_state)
        # 如果该prompt_id已经完成，删除缓存 让用户可以继续生图
        gen_type = cache_redis.get(prompt_tasktype_fmt.format(prompt_id))
        prompt_id_email = cache_redis.get(PROCESSING_PROMPT_TO_EMAIL_FMT.format(gen_type, prompt_id))
        if prompt_id_email:
            cache_redis.delete(PROCESSING_EMAIL_TO_PROMPT_FMT.format(gen_type, prompt_id_email))
        comfy_address = task_redis.get(prompt_id_comfy_key)
        decrement_task(server=comfy_address)
        error_logger.error(f"gen image error {prompt_id}, {prompt_data}")


@celery_app.task()
def gen_task(task_id: int, gen_type: str, userinfo: dict, extra_data: dict, comfy_address: str, _type='img2img'):
    """
    异步调用comfyui生成任务
    :param task_id: 任务id
    :param gen_type: 任务类型
    :param userinfo: 用户信息
    :param extra_data: 参数
    :param comfy_address: comfy地址
    :param _type: 类型 image/video 分别代表图生图和视频生视频
    :return:
    """
    start_time = timer()
    type_conf = {
        'img2img': {
            'source_key': 'img_url',
            'tool_class': Img2ImgToolComfy
        },
        'video2img': {
            'source_key': 'video_url',
            'tool_class': Video2VideoToolComfy
        },
        'text2img': {
            'source_key': '',
            'tool_class': Text2ImgToolComfy
        }
    }
    if _type not in type_conf:
        error_logger.error(f"gen image failed, task type error: {_type}")
        update_task_by_id(task_id, task_state=4, error=f"gen image failed, task type error: {_type}")
        return
    source_key = type_conf[_type]['source_key']
    tool_class = type_conf[_type]['tool_class']

    email = userinfo['email']
    source_url = extra_data.get(source_key)
    other_params = get_other_params(gen_type, extra_data)
    number = extra_data.get('number', None)
    func_gen_type = other_params.pop("functype", None)
    try:
        obj = tool_class(server_address=comfy_address, number=number)
        prompt_id = obj.gen(source_url=source_url, _type=func_gen_type, **other_params)
        end_time = timer()
        prompt_logger.info(
            f"create prompt success,email: {email}, task_type: {gen_type}, task_id: {task_id}, prompt_id: {prompt_id}, comfy_address: {comfy_address}, cost: {end_time - start_time}s")
        record_service_error(server=comfy_address, success=True)  # 成功的话清空服务错误记录
    except Exception as e:
        record_service_error(server=comfy_address)  # 记录服务错误
        if is_server_excluded(server=comfy_address):
            # 到达一个的阈值就将该地址下线
            offline_resource.delay(comfy_address)
        end_time = timer()
        error_logger.error(f"task_id: {task_id} gen image failed, task to comfy exception: {str(e)}")
        decrement_task(server=comfy_address)
        update_task_by_id(task_id, task_state=4, error=f"gen image failed, due to: {str(e)}")
        prompt_logger.error(
            f"create prompt failed, email: {email}, task_type: {gen_type}, task_id: {task_id}, comfy_address: {comfy_address}, cost: {end_time - start_time}s , error: {str(e)}")
        return
    if not prompt_id:
        decrement_task(server=comfy_address)
        error_logger.error(f"gen image failed, task to comfy no prompt_id")
        update_task_by_id(task_id, task_state=4, error="gen image failed, no prompt id")
        return
    # 保存promptid对应的comfy地址
    prompt_id_comfy_key = prompt_comfy_address_fmt.format(prompt_id)
    task_redis.set(prompt_id_comfy_key, comfy_address, ex=60 * 60)

    # 设立对应关系的缓存 确保每个人类型的任务一个人只能生成一次
    cache_redis.set(PROCESSING_EMAIL_TO_PROMPT_FMT.format(gen_type, email), prompt_id,
                    ex=60 * 5)  # email对应prompt_id
    cache_redis.set(PROCESSING_PROMPT_TO_EMAIL_FMT.format(gen_type, prompt_id), email,
                    ex=60 * 5)  # prompt_id对应email
    cache_redis.set(prompt_tasktype_fmt.format(prompt_id), gen_type, ex=3600)  # prompt_id对应gen_type

    update_task_by_id(task_id, task_state=1, prompt_id=prompt_id)


@celery_app.task()
def offline_resource(address):
    """
    服务器下线
    :return:
    """
    if not acquire_lock(lock_name=address, lock_timeout=600):
        # 防止短时间内重复下线
        return
    cache_data = cache_redis.get(COMFY_RESOURCE_KEY)
    cache_data = json.loads(cache_data) if cache_data else []
    if not cache_data:
        return
    tag, desc = "", ""
    for item in cache_data:
        if item["address"] == address:
            item["status"] = False
            tag = item.get("tag", "")
            desc = item.get("desc", "")
    cache_redis.set(COMFY_RESOURCE_KEY, json.dumps(cache_data), 60 * 30)
    message = f"offline: {address}, {tag}, {desc}"
    resource_logger.info(message)
    try:
        log_output1, log_output2 = restart_comfyui(address)  # 重启comfyui
    except Exception as e:
        resource_logger.info(str(e))
        log_output1, log_output2 = "", ""
    try:
        url = "https://oapi.dingtalk.com/robot/send?access_token=ecbaf7ecb6ebead423a75737159bfffe079899238c3a7e4e561d4aa7884e820e"
        data = {
            "msgtype": "text",
            "text": {
                "content": f"Aiease服务器下线重启\n{message}\n重启前日志:{log_output1}\n重启后日志:{log_output2}",
            }
        }
        requests.post(url, headers={'Content-Type': 'application/json'}, json=data, verify=False, timeout=5)
    except Exception as e:
        resource_logger.info(str(e))
    if "0.0.0.0" in log_output2:
        # 重启成功后删除缓存 0.0.0.0表示重启成功
        cache_redis.delete(COMFY_RESOURCE_KEY)
