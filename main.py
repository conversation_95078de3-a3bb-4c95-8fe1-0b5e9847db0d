import datetime

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi_utils.timing import add_timing_middleware

from conf.config import settings
from core import Events, Router
from core.Exception import http_error_handler, http422_error_handler
from fastapi.middleware.cors import CORSMiddleware
from conf.settings import DEBUG

application = FastAPI(
    debug=DEBUG,
    title=settings.APP_TITLE,
    version=settings.VERSION,
    description="aiease gen image api"
)

application.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

application.add_exception_handler(HTTPException, http_error_handler)
application.add_exception_handler(RequestValidationError, http422_error_handler)

# 事件监听
application.add_event_handler("startup", Events.startup(application))
application.add_event_handler("shutdown", Events.stopping(application))

# 路由
application.include_router(Router.router)


@application.get("/health")
async def health_check():
    return {"status": "ok", "time": datetime.datetime.now()}


app = application

add_timing_middleware(app)
